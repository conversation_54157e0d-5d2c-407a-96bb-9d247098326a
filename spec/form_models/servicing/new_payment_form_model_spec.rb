# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Servicing::NewPaymentFormModel, type: :model do
  let(:remaining_balance) { 12_345.05 }
  let(:min_payment_date) { 1.day.from_now.beginning_of_day }
  let(:max_payment_date) { 1.month.from_now.beginning_of_day }
  let(:bank_account) { 'Chase (...1234)' }
  let(:params) do
    {
      remaining_balance:,
      min_payment_date:,
      max_payment_date:,
      bank_account:,
      payment_date: 2.weeks.from_now,
      payment_amount: 100.00
    }
  end

  subject { described_class.new(params) }

  describe 'formatting' do
    context 'strips mask from currency fields' do
      it 'for payment_amount' do
        subject.payment_amount = '$1,234.44'
        expect(subject.payment_amount).to eq(1234.44)
      end

      it 'for payment_custom' do
        subject.payment_custom = '$1,234.44'
        expect(subject.payment_custom).to eq(1234.44)
      end
    end
  end

  describe 'validations' do
    context 'when all attributes are valid' do
      it 'is valid' do
        expect(subject).to be_valid
      end
    end

    context 'when bank_account' do
      it 'is not valid' do
        subject.bank_account = nil
        expect(subject).not_to be_valid
        expect(subject.errors[:bank_account]).to include(/error occurred while loading your bank account/)
      end
    end

    context 'when payment_date' do
      it 'is not valid' do
        subject.payment_date = nil
        expect(subject).not_to be_valid
        expect(subject.errors[:payment_date]).to include(/select a valid date/)
      end

      it 'is on the payment date' do
        subject.payment_date = min_payment_date
        expect(subject).to be_valid
      end

      it 'is before the payment date' do
        subject.payment_date = min_payment_date - 1.day
        expect(subject).not_to be_valid
        expect(subject.errors[:payment_date]).to include(/enter a valid date on or after/)
      end

      it 'is on the next payment date' do
        subject.payment_date = max_payment_date
        expect(subject).to be_valid
      end

      it 'is after the next payment date' do
        subject.payment_date = max_payment_date + 1.day
        expect(subject).not_to be_valid
        expect(subject.errors[:payment_date]).to include(/enter a valid date before/)
      end
    end

    context 'when payment_amount' do
      it 'is not valid' do
        subject.payment_amount = nil
        expect(subject).not_to be_valid
        expect(subject.errors[:payment_amount]).to include(/amount is required/)
      end

      it 'is zero' do
        subject.payment_amount = 0.0
        expect(subject).not_to be_valid
        expect(subject.errors[:payment_amount]).to include(/amount must be greater than/)
      end

      it 'is the payoff' do
        subject.payment_amount = remaining_balance
        expect(subject).to be_valid
      end

      it 'is too high' do
        subject.payment_amount = remaining_balance + 1
        expect(subject).not_to be_valid
        expect(subject.errors[:payment_amount]).to include(/amount cannot be greater than your current payoff/)
      end
    end
  end
end
