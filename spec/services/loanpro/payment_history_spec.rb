# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Loanpro::PaymentHistory do
  let(:loan_id) { '7849' }
  let(:transactions_response) { { 'results' => [{ 'id' => 11, 'paymentId' => 778 }, { 'id' => 12, 'paymentId' => 0 }] } }
  let(:payments_response) { { 'results' => [{ 'id' => 0, 'date' => '/Date(1746662400)/' }, { 'id' => 778, 'date' => '/Date(1748908800)/' }] } }

  before do
    allow(Clients::LoanproApi).to receive(:get_payments).and_return(payments_response)
    allow(Clients::LoanproApi).to receive(:get_transactions).and_return(transactions_response)
  end

  subject { described_class.new(loan_id:) }

  describe '#call' do
    context 'when a loan has payments and transactions' do
      it 'calls the LoanPro API to fetch payments and transactions' do
        subject.call

        expect(Clients::LoanproApi).to have_received(:get_payments)
        expect(Clients::LoanproApi).to have_received(:get_transactions)
      end
    end

    context 'when call to loanpro api returns a error' do
      before do
        allow(Clients::LoanproApi).to receive(:get_payments).and_raise(Faraday::Error.new('Connection error'))
        allow(Rails.logger).to receive(:error).with('Error in PaymentHistory', hash_including(error_message: 'Connection error'))
      end
      it 'logs and raises an error' do
        expect(Rails.logger).to receive(:error).with('Error in PaymentHistory', hash_including(error_message: 'Connection error'))
        expect { subject.call }.to raise_error(Loanpro::PaymentHistory::PaymentHistorySystemError)
      end
    end

    context 'when payments or transactions is not present for a given loan' do
      let(:payments_response) { {} }
      let(:transactions_response) { {} }

      it 'should return a empty array' do
        expect(subject.call).to eq([])
      end
    end
  end
end
