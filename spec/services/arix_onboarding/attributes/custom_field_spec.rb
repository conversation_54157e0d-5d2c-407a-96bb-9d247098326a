# frozen_string_literal: true

require 'rails_helper'

RSpec.describe ArixOnboarding::Attributes::CustomField do
  let(:agreement_pdf_name) { 'CRB Installment Loan Agreement_version_9_John_Doe_123456789.pdf' }
  let(:beyond_enrollment_date) { Date.today }
  let(:cashout_amount) { -1.0 }
  let(:code) { Faker::Alphanumeric.alpha(number: 10) }
  let(:decision_engine_input_double) do
    instance_double(ArixOnboarding::FundingDocuments::DecisionEngineInput,
                    bankruptcy_count: 0,
                    charge_offs_within_6_months_count: 1,
                    charge_offs_within_36_months_count: 1,
                    credit_cards_opened_730_days_pre_beyond_count: rand(0..10),
                    derogatory_trade_count: 0,
                    ipl_previously_declined?: true,
                    past_due_60_days_count: 7,
                    previously_declined_for_only_passable_reasons?: true,
                    recent_bankruptcy_within_7_years_count: 1,
                    petitioned_bankruptcy_count: 2,
                    revolving_credit_limit: 1,
                    revolving_unpaid_balance: 1,
                    recent_auto_delinquency_3_months_count: 3,
                    recent_auto_delinquency_6_months_count: 4,
                    recent_mortgage_delinquency_3_months_count: 5,
                    recent_mortgage_delinquency_6_months_count: 6,
                    recent_inquiries_within_3_months_count: 1,
                    recent_inquiries_within_6_months_count: 1,
                    settled_trades_paid_charge_offs_paid_collecions_pre_beyond_count: rand(0..10),
                    trades_opened_within_730_days_pil_adjusted_count: rand(0..10))
  end
  let(:decision_engine_output_double) do
    instance_double(ArixOnboarding::FundingDocuments::DecisionEngineOutput,
                    version_policy: 1.5,
                    num_inquiries_within_120_days_capped: 1,
                    program_first_application_capped: 1,
                    fico_beacon5_capped: 614,
                    trades_opened_730_days_pil_adjusted_capped: 3,
                    loan_to_income_capped: 2,
                    number_of_returned_deposits_in_last_180_days_capped: 3,
                    number_bankruptcy_or_chapter_7_ever_capped: 3,
                    pct_opened_of_ever_past_12mos_pre_beyond_capped: 5,
                    num_consumer_dispute_indicator_capped: 2,
                    num_open_mortgage_capped: 1,
                    employed_full_time_capped: 1,
                    payment_shock_capped: 0.123,
                    model_logistic_score: 0.0234,
                    credit_model_version: 1.0)
  end
  let(:estimated_cft_deposits) { Faker::Number.decimal(l_digits: 2).to_s }
  let(:installment_loan_agreement) { create(:doc, loan:, template:, name: agreement_pdf_name) }
  let(:lead) { create(:lead, code:, program_id:, service_entity_name:) }
  let(:loan_detail) do
    create(:loan_detail, loan:, beyond_enrollment_date:, estimated_cft_deposits:, payment_adherence_ratio_3_months:, payment_adherence_ratio_6_months:, total_amount_enrolled_debt:)
  end
  let(:monthly_deposit_amount) { Faker::Number.decimal(l_digits: 3).to_s }
  let(:monthly_housing_payment) { Faker::Number.decimal(l_digits: 3).to_s }
  let!(:offer) { create(:offer, loan:, cashout_amount:) }
  let(:payment_adherence_ratio_3_months) { Faker::Number.decimal(l_digits: 1).to_d }
  let(:payment_adherence_ratio_6_months) { Faker::Number.decimal(l_digits: 1).to_d }
  let(:program_id) { Faker::Number.number(digits: 6) }
  let(:service_entity_name) { 'Five Lakes' }
  let(:socure_double) do
    instance_double(ArixOnboarding::FundingDocuments::Socure, global_watchlist: '<globalWatchlist>test global watch list/globalWatchlist>', kyc: '<kyc>test kyc</kyc>')
  end
  let(:template) { create(:doc_template, type: template_type) }
  let(:template_type) { DocTemplate::TYPES[:INSTALLMENT_LOAN_AGREEMENT] }
  let(:total_amount_enrolled_debt) { Faker::Number.decimal(l_digits: 3).to_s }

  let(:custom_field5_attributes) do
    {
      MonthlyHousingExpense: monthly_housing_payment,
      CreditPolicyVersion: 1.5,
      TotalAmountEnrolledDebt: total_amount_enrolled_debt,
      DebtResolutionProgramEnrollmentDate: beyond_enrollment_date&.to_date&.iso8601,
      DebtResolutionProgramEnrollmentStatus: 'Enrolled',
      DebtResolutionProgramName: service_entity_name,
      CashBackOffered: false,
      CashBackAmount: cashout_amount.to_s,
      NumCreditCardsOpened730DaysPreBeyond: decision_engine_input_double.credit_cards_opened_730_days_pre_beyond_count,
      NumTradesOpened730DaysPilAdjusted: decision_engine_input_double.trades_opened_within_730_days_pil_adjusted_count,
      NumSettledTradesPaidChargeOffsPaidCollections: decision_engine_input_double.settled_trades_paid_charge_offs_paid_collecions_pre_beyond_count,
      PreviouslyDeclinedForAboveLoan: false
    }
  end

  let(:custom_field5_attributes_upl) do
    {
      CreditPolicyVersion: decision_engine_output_double.version_policy,
      Financial_Institution_Check: false,
      Num_60DaysPastDue: decision_engine_input_double.past_due_60_days_count,
      Num__ChargeOffs_Within6Months: decision_engine_input_double.charge_offs_within_6_months_count,
      Num_Bankruptcy: decision_engine_input_double.bankruptcy_count,
      Num_ChargeOffs_Within36Months: decision_engine_input_double.charge_offs_within_36_months_count,
      Num_DerogatoryTrades: decision_engine_input_double.derogatory_trade_count,
      Num_RecentInquiries_LessThan3Months: decision_engine_input_double.recent_inquiries_within_3_months_count,
      Num_RecentInquiries_LessThan6Months: decision_engine_input_double.recent_inquiries_within_6_months_count,
      Sum_RevolvingCreditLimitAmount: decision_engine_input_double.revolving_credit_limit,
      Sum_RevolvingUnpaidBalanceAmount: decision_engine_input_double.revolving_unpaid_balance
    }
  end

  let(:custom_field6_attributes) do
    {
      DscNsfs3Months: 0.0,
      DscPaymentAdheranceRatio3Months: (payment_adherence_ratio_3_months * 100),
      DscPaymentAdheranceRatio6Months: (payment_adherence_ratio_6_months * 100),
      DscPaymentAmount: monthly_deposit_amount,
      DscAccountBalance: estimated_cft_deposits
    }
  end

  let(:custom_field7_attributes) do
    {
      NumRecentBankruptcyWithin7Years: 1,
      NumPetitionedBankruptcy: 2,
      NumRecentAutoDelinquency3Months: 3,
      NumRecentAutoDelinquency6Months: 4,
      NumRecentMortgageDelinquency3Months: 5,
      NumRecentMortgageDelinquency6Months: 6
    }
  end

  subject { described_class.new(loan, loan_detail, lead, installment_loan_agreement) }

  before do
    allow(ArixOnboarding::FundingDocuments::Socure).to receive(:new).and_return(socure_double)
    allow(ArixOnboarding::FundingDocuments::DecisionEngineInput).to receive(:new).and_return(decision_engine_input_double)
    allow(ArixOnboarding::FundingDocuments::DecisionEngineOutput).to receive(:new).and_return(decision_engine_output_double)
  end

  describe '#attr' do
    describe 'when the product_type is UPL' do
      let(:loan) { create(:upl_loan, code:, monthly_housing_payment:, monthly_deposit_amount:, program_id:) }

      it 'returns the expected attributes' do
        result = subject.attr

        expect(result[:Field2]).to eq('<globalWatchlist>test global watch list/globalWatchlist>')
        expect(result[:Field4]).to eq(agreement_pdf_name)
        expect(result[:Field5]).to eq(custom_field5_attributes_upl.to_json)
        expect(result[:Field7]).to eq(custom_field7_attributes.to_json)
      end

      context 'fraud_alert is approved' do
        let!(:todo) { create(:todo, loan:, type: 'fraud_alert', status: 'approved') }
        it 'return socure kyc and fraud alert verification' do
          result = subject.attr
          field3 = JSON.parse(result[:Field3])
          expect(field3['kyc']).to eq('<kyc>test kyc</kyc>')
          expect(field3['CIP Verification used']['fraud']).to eq('BorrowerIdentityConfirmed')
        end
      end

      context 'fraud_alert is not approved' do
        let!(:todo) { create(:todo, loan:, type: 'fraud_alert', status: 'rejected') }
        it 'return socure kyc' do
          result = subject.attr
          field3 = JSON.parse(result[:Field3])
          expect(field3['kyc']).to eq('<kyc>test kyc</kyc>')
        end
      end

      context 'fraud_alert todo is not present' do
        let!(:todo) { nil }
        it 'return socure kyc' do
          result = subject.attr
          field3 = JSON.parse(result[:Field3])
          expect(field3['kyc']).to eq('<kyc>test kyc</kyc>')
        end
      end

      context 'bank is not approved' do
        let!(:todo) { create(:todo, loan:, type: 'bank', status: 'rejected') }
        it 'return financial institution check as false' do
          result = subject.attr
          field3 = JSON.parse(result[:Field5])
          expect(field3['Financial_Institution_Check']).to eq(false)
        end
      end

      context 'bank has no approved document' do
        let!(:todo) { create(:todo, loan:, type: 'bank', status: 'approved') }
        let!(:todo_doc) { create(:todo_doc, todo:, status: 'rejected') }
        it 'return financial institution check as false' do
          result = subject.attr
          field3 = JSON.parse(result[:Field5])
          expect(field3['Financial_Institution_Check']).to eq(false)
        end
      end

      context 'bank has approved document' do
        let!(:todo) { create(:todo, loan:, type: 'bank', status: 'approved') }
        let!(:todo_doc) { create(:todo_doc, todo:, status: 'approved') }
        it 'return financial institution check as true' do
          result = subject.attr
          field3 = JSON.parse(result[:Field5])
          expect(field3['Financial_Institution_Check']).to eq(true)
        end
      end
    end

    describe 'when the product_type is IPL' do
      let(:loan) { create(:ipl_loan, code:, monthly_housing_payment:, monthly_deposit_amount:, program_id:) }

      it 'returns the expected attributes' do
        result = subject.attr
        field3 = JSON.parse(result[:Field3])
        expect(field3['kyc']).to eq('<kyc>test kyc</kyc>')
        expect(result[:Field5]).to eq(custom_field5_attributes.to_json)
        expect(result[:Field6]).to eq(custom_field6_attributes.to_json)
      end

      context 'PreviouslyDeclinedForAboveLoan attribute' do
        context 'when the applicant was not previously declined' do
          before do
            allow(decision_engine_input_double).to receive(:ipl_previously_declined?).and_return(false)
          end

          it 'is false' do
            result = subject.attr
            field5 = JSON.parse(result[:Field5])
            expect(field5['PreviouslyDeclinedForAboveLoan']).to be false
          end
        end

        context 'when the applicant was previously declined for passable reasons' do
          before do
            allow(decision_engine_input_double).to receive(:ipl_previously_declined?).and_return(true)
            allow(decision_engine_input_double).to receive(:previously_declined_for_only_passable_reasons?).and_return(true)
          end

          it 'is false' do
            result = subject.attr
            field5 = JSON.parse(result[:Field5])
            expect(field5['PreviouslyDeclinedForAboveLoan']).to be false
          end
        end

        context 'when the applicant was previously declined for non-passable reasons' do
          before do
            allow(decision_engine_input_double).to receive(:ipl_previously_declined?).and_return(true)
            allow(decision_engine_input_double).to receive(:previously_declined_for_only_passable_reasons?).and_return(false)
          end

          it 'is true' do
            result = subject.attr
            field5 = JSON.parse(result[:Field5])
            expect(field5['PreviouslyDeclinedForAboveLoan']).to be true
          end
        end

        context 'Credit Model Arix Variables',
                with_feature_flag: %i[credit_model_arix_fields experiment_2025_04_CHI_1753_Credit_Model_1_0] do
          let(:experiment) { '2025_04_CHI_1753_Credit_Model_1_0' }

          # @experiment_cohort ||= Experiment['2025_04_CHI_1753_Credit_Model_1_0'].fetch_cohort_for(loan.borrower)

          it 'returns expected attributes' do
            create(:experiment_subject, experiment:, subject: loan.borrower, cohort: 'challenger')

            expected_attributes = {
              CreditPolicyVersion: 1.5,
              CreditModelVersion: 1.0,
              EmployedFullTimeCapped: decision_engine_output_double.employed_full_time_capped,
              FicoBeacon5Capped: decision_engine_output_double.fico_beacon5_capped,

              LoanToIncomeCapped: decision_engine_output_double.loan_to_income_capped,
              ModelLogisticScore: decision_engine_output_double.model_logistic_score,
              NumBankruptcyOrChapter7EverCapped: decision_engine_output_double.number_bankruptcy_or_chapter_7_ever_capped,
              NumConsumerDisputeIndicatorCapped: decision_engine_output_double.num_consumer_dispute_indicator_capped,
              NumInquiries120DaysCapped: decision_engine_output_double.num_inquiries_within_120_days_capped,
              NumOpenMortgageCapped: decision_engine_output_double.num_open_mortgage_capped,
              NumTradesOpened730DaysPilAdjustedCapped: decision_engine_output_double.trades_opened_730_days_pil_adjusted_capped,
              NumberOfReturnedDepositsInLast180DaysCapped: decision_engine_output_double.number_of_returned_deposits_in_last_180_days_capped,
              PaymentShockCapped: decision_engine_output_double.payment_shock_capped,
              PctOpenedOfEverPast12MosPreBeyondCapped: decision_engine_output_double.pct_opened_of_ever_past_12mos_pre_beyond_capped,
              ProgramFirstApplicationCapped: decision_engine_output_double.program_first_application_capped
            }.stringify_keys
            result = subject.attr
            field5 = JSON.parse(result[:Field5])

            expect(field5).to match(hash_including(expected_attributes))
          end

          it 'does not send for the champion' do
            result = subject.attr
            field5 = JSON.parse(result[:Field5])

            expect(field5).not_to have_key('NumInquiries120Days')
          end
        end
      end
    end
  end
end
