# frozen_string_literal: true

require 'rails_helper'

module ArixOnboarding
  RSpec.describe FetchOnboardingPayloads do
    let(:loan) { create(:loan) }
    let(:base_time) { Time.now }
    let(:bucket) { Rails.application.config_for(:aws).aws_s3_bucket_name }
    let(:unified_id) { loan.unified_id }
    let(:prefix) { "arix_request_payloads/#{Rails.env}/#{loan.id}/loan" }
    let(:body) { double(read: '{ "AssetClass": 3 }') }
    let(:mock_content1) { double(key: "#{prefix}_creation-#{base_time.to_i}.json", last_modified: base_time, body:) }
    let(:mock_s3_contents) { [mock_content1] }
    let(:mock_s3_response) { double(contents: mock_s3_contents) }
    let(:mock_s3_client) { instance_double(Aws::S3::Client, list_objects_v2: mock_s3_response) }

    before { allow(mock_s3_client).to receive(:get_object).with(bucket:, key: mock_content1.key).and_return(mock_content1) }

    subject { described_class.call(unified_id:) }

    describe '.call' do
      before { allow(Aws::S3::Client).to receive(:new).and_return(mock_s3_client) }

      it 'returns the arix onboarding payload body from S3' do
        expect(subject).to eq([{
          bucket:,
          key: "#{prefix}_creation-#{base_time.to_i}.json",
          operation: 'creation',
          timestamp: base_time.to_i.to_s,
          last_modified: base_time.iso8601,
          content: { 'AssetClass' => 3 }
        }.stringify_keys])
      end

      context 'with multiple files' do
        let(:base_time2) { Time.now - 1.day }
        let(:body2) { double(read: '{ "AssetClass": 4 }') }
        let(:mock_content2) { double(key: "#{prefix}_update-#{base_time2.to_i}.json", last_modified: base_time2, body: body2) }
        let(:mock_s3_contents) { [mock_content1, mock_content2] }

        before { allow(mock_s3_client).to receive(:get_object).with(bucket:, key: mock_content2.key).and_return(mock_content2) }

        it 'returns the both arix onboarding payloads' do
          expect(subject).to eq([{
            bucket:,
            key: "#{prefix}_creation-#{base_time.to_i}.json",
            operation: 'creation',
            timestamp: base_time.to_i.to_s,
            last_modified: base_time.iso8601,
            content: { 'AssetClass' => 3 }
          }, {
            bucket:,
            key: "#{prefix}_update-#{base_time2.to_i}.json",
            operation: 'update',
            timestamp: base_time2.to_i.to_s,
            last_modified: base_time2.iso8601,
            content: { 'AssetClass' => 4 }
          }].map(&:stringify_keys))
        end
      end

      context 'when there is no active record loan for a given unified id' do
        let(:unified_id) { '111111111' }

        it 'raises an ActiveRecord::RecordNotFound error' do
          expect { subject }.to raise_error(ActiveRecord::RecordNotFound)
        end
      end

      context 'when body is not a proper JSON' do
        let(:body) { double(read: '{') }

        it 'logs an error and returns a blank array' do
          expect(Rails.logger).to receive(:info).once.with(<<~MESSAGE.squish, content: body.read)
            ArixOnboarding::FetchOnboardingPayloads - Unable to parse JSON for #{mock_content1.key}: expected object key, got ' at line 1 column 2
          MESSAGE

          expect(Rails.logger).to receive(:info).once.with(<<~MESSAGE.squish)
            ArixOnboarding::FetchOnboardingPayloads - No objects found in directory:
            arix_request_payloads/#{Rails.env}/#{loan.id} for unified_id: #{unified_id}
          MESSAGE

          expect(subject).to eq([])
        end
      end
    end
  end
end
