# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Documents::StoreDocument do
  let(:document) { build(:contract_document) }
  let(:ip_address) { '127.0.0.1' }
  let(:loan) { create(:loan) }

  subject { described_class.new(document:, ip_address:, loan:) }

  describe '.call' do
    let(:s3_client) { instance_double(Aws::S3::Client, put_object: true) }
    let(:s3_bucket_name) { Rails.application.config_for(:contract_documents).bucket_name }
    let(:s3_key_prefix) { Rails.application.config_for(:contract_documents).signed_docs_prefix }

    before do
      allow(Aws::S3::Client).to receive(:new).and_return(s3_client)
      special_document_types = [DocTemplate::TYPES[:AA], DocTemplate::TYPES[:NOTICE_OF_INCOMPLETE_APPLICATION]]
      regular_document_type = (DocTemplate::TYPES.values - special_document_types).sample
      document.template.type = regular_document_type
    end

    it 'records the document in S3' do
      subject.call
      expect(s3_client).to have_received(:put_object).with(bucket: s3_bucket_name, key: "#{s3_key_prefix}/#{document.filename}", body: document.content)
    end

    it 'creates a Doc record for the document' do
      expect { subject.call }.to change(Doc, :count).by(1)

      new_doc = Doc.order(created_at: :desc).first
      expect(new_doc.template).to eq(document.template)
      expect(new_doc.name).to eq(document.filename)
      expect(new_doc.uri).to eq("#{s3_key_prefix}/#{document.filename}")
      expect(new_doc.ip_address).to eq(ip_address)
      expect(new_doc.loan_id).to eq(loan.id)
    end

    context 'when the document being stored is a NOAA' do
      let(:s3_key_prefix) { Rails.application.config_for(:contract_documents).adverse_action_prefix }

      before { document.template.type = DocTemplate::TYPES[:AA] }

      it 'uses the Adverse Action document S3 key prefix' do
        subject.call
        expect(s3_client).to have_received(:put_object).with(bucket: s3_bucket_name, key: "#{s3_key_prefix}/#{document.filename}", body: document.content)
      end
    end

    context 'when the document being stored is a Notice of Incomplete Application' do
      let(:s3_key_prefix) { Rails.application.config_for(:contract_documents).incomplete_application_prefix }

      before { document.template.type = DocTemplate::TYPES[:NOTICE_OF_INCOMPLETE_APPLICATION] }

      it 'uses the Incomplete Application document S3 key prefix' do
        subject.call
        expect(s3_client).to have_received(:put_object).with(bucket: s3_bucket_name, key: "#{s3_key_prefix}/#{document.filename}", body: document.content)
      end
    end

    context 'when no IP address is specified' do
      it 'records a default IP address value' do
        expect { subject.call }.to change(Doc, :count).by(1)
        new_doc = Doc.order(created_at: :desc).first
        expect(new_doc.ip_address).to eq('127.0.0.1')
      end
    end
  end
end
