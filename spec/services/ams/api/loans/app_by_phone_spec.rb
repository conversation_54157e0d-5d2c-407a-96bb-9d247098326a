# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Ams::Api::Loans::AppByPhone, type: :service do
  include ServiceObjectHelper
  include_context 'service with authentication'

  let(:code) { Faker::Number.number(digits: 8).to_s }
  let(:request_id) { SecureRandom.uuid }
  let(:email) { Faker::Internet.email }
  let!(:lead) { create(:lead, code:, type: 'IPL') }
  let(:service_object) { described_class.new(**params) }
  let(:borrower_params) do
    {
      email:,
      first_name: Faker::Name.first_name,
      last_name: Faker::Name.last_name,
      ssn: Faker::Number.number(digits: 9).to_s,
      address_street: Faker::Address.street_address,
      address_apt: Faker::Address.secondary_address,
      city: Faker::Address.city,
      state: Faker::Address.state_abbr,
      zip_code: Faker::Number.number(digits: 5).to_s,
      phone_number: Faker::Number.number(digits: 10).to_s,
      housing_status: Loan.housing_statuses.keys.sample,
      time_at_residence: Loan.time_at_residences.keys.sample,
      employment_status: Loan.employment_statuses.keys.sample,
      income: Faker::Number.between(from: 30_000, to: 60_000),
      date_of_birth: Faker::Date.birthday(min_age: 18, max_age: 65).strftime('%m-%d-%Y'),
      employment_start_date: Faker::Date.between(from: 5.years.ago, to: 1.year.ago).strftime('%Y/%M'),
      employment_industry: Loan.employment_industries.keys.sample,
      monthly_housing_payment: Faker::Number.between(from: 500, to: 1500),
      employment_pay_frecuency: Loan.employment_pay_frecuencies.keys.sample,
      education_level: Loan.education_levels.keys.sample,
      employment_last_pay_date: Date.today.strftime('%Y/%m/%d'),
      tcpa_accepted: [true, false].sample
    }
  end
  let(:loan_params) do
    {
      amount: Faker::Number.between(from: 30_000, to: 60_000),
      code:,
      product_type: 'IPL',
      credit_score_range: Loan.credit_score_ranges.keys.sample,
      loan_creation_date: Faker::Time.between(from: 1.year.ago, to: Date.today).utc.strftime('%Y-%m-%d')
    }
  end
  let(:params) { { request_id:, **borrower_params, **loan_params } }
  let(:identity_id) { SecureRandom.uuid }

  before do
    Current.oauth_token = 'token'
    stub_request(:patch, "http://beyond.com/api/lending/programs/#{lead.program_id}/loan") if lead&.program_id
    allow(Users::CreateUser).to receive(:call).and_call_original
  end

  specify 'validations' do
    expect(service_object).to_not allow_value('XMAS').for(:product_type)
    expect(service_object).to allow_value('IPL').for(:product_type)

    expect(service_object).to_not allow_value('wrong email').for(:email)
    expect(service_object).to allow_value('<EMAIL>').for(:email)
  end

  describe '#call' do
    subject(:call) { service_object.call }

    it 'calls Users::CreateUser' do
      expect(Users::CreateUser).to receive(:call).and_call_original
      call
    end

    shared_examples 'a successful request' do
      it 'returns a 201 status' do
        call
        expect(service_object.status).to eq(201)
      end

      it 'returns the correct body' do
        call
        expect(service_object.body).to match({
                                               loan_app: LoanBlueprint.render_as_hash(service_object.loan),
                                               borrower: BorrowerBlueprint.render_as_hash(service_object.borrower, view: :app_by_phone),
                                               credit_test_1: nil
                                             })
      end
    end

    describe 'stubbing loan amount' do
      context 'when the amount is missing from the request' do
        before do
          loan_params.delete(:amount)
        end

        it 'stubs the amount' do
          expect(call.amount).to eq(0)
          expect(service_object.loan.amount).to eq(0)
        end
      end

      context 'when the amount is provided in the request' do
        it 'uses the provided amount' do
          expect(call.amount).to eq(loan_params[:amount])
          expect(service_object.loan.amount).to eq(loan_params[:amount])
        end
      end
    end

    describe 'without existing records' do
      it 'creates a loan record' do
        expect { call }.to change(Loan, :count).by(1)
        expect(call.loan).to be_persisted
      end

      it 'creates a loan details record' do
        expect { call }.to change(LoanDetail, :count).by(1)
      end

      it 'creates a loan payment details record' do
        expect { call }.to change(LoanPaymentDetail, :count).by(1)
      end

      it 'creates tradeline records' do
        expect { call }.to change(LoanPaymentDetail, :count).by(1)
      end

      it 'creates a borrower' do
        expect { call }.to change(Borrower, :count).by(1)
        expect(call.borrower).to be_persisted
      end

      it 'calls unique_unified_id to create a unified id' do
        expect(service_object).to receive(:unique_unified_id)

        call
      end

      it_behaves_like 'a successful request'
    end

    describe 'with existing records' do
      let!(:borrower) { create(:borrower, email:) }
      let(:loan_app_status) { create(:loan_app_status, name: 'ADD_INFO_COMPLETE') }
      let!(:loan) { create(:loan, borrower:, code:, request_id:, product_type: 'IPL', loan_app_status:) }
      let!(:loan_payment_detail) { create(:loan_payment_detail, loan:) }
      let!(:loan_detail) { create(:loan_detail, loan:) }
      let!(:tradeline_detail) { create(:loan_tradeline_detail, loan:, **lead.tradeline_details[0]) }

      context 'when the loan with matching code and request_id has an active status' do
        it 'does not update the existing loan or add a new loan' do
          expect { call }.to not_change(Loan, :count).and not_change(loan, :created_at).and not_change(loan, :request_id)
          expect(service_object.loan).to be_persisted
        end

        it 'does not create a loan details record' do
          expect { call }.to_not change(LoanDetail, :count)
        end

        it 'does not create a loan payment details record' do
          expect { call }.to_not change(LoanPaymentDetail, :count)
        end

        it 'does not create a tradeline record' do
          expect { call }.to_not change(LoanTradelineDetail, :count)
        end

        it 'does not create a borrower record' do
          expect { call }.to_not change(Borrower, :count)
          expect(service_object.borrower).to be_persisted
        end

        it 'does not return a hash' do
          expect(call.class).not_to eq(Hash)
        end

        it_behaves_like 'a successful request'
      end

      context 'when there is a loan with a matching code and request_id that is in the basic info complete status' do
        let!(:loan_app_status) { LoanAppStatus.for('BASIC_INFO_COMPLETE') }
        let!(:borrower) { create(:borrower, email:, identity_id: SecureRandom.uuid) }
        let!(:borrower_additional_info) { create(:borrower_additional_info, borrower:, loan:) }
        let!(:loan) { create(:loan, borrower:, code:, request_id:, product_type: 'IPL', loan_app_status:) }
        let(:new_borrower_additional_info_attributes) do
          {
            address_street: borrower_params[:address_street],
            address_apt: borrower_params[:address_apt],
            city: borrower_params[:city],
            state: borrower_params[:state],
            zip_code: borrower_params[:zip_code],
            phone_number: borrower_params[:phone_number]
          }
        end

        it_behaves_like 'a successful request'

        it 'updates the borrower record attributes' do
          call
          borrower.reload
          expect(borrower.first_name).to eq(borrower_params[:first_name])
          expect(borrower.last_name).to eq(borrower_params[:last_name])
          expect(borrower.date_of_birth).to eq(Date.strptime(borrower_params[:date_of_birth], '%m-%d-%Y'))
          expect(borrower.ssn).to eq(borrower_params[:ssn])
        end

        it 'updates the borrower additional info record attributes' do
          call
          borrower_additional_info.reload
          expect(borrower_additional_info).to have_attributes(new_borrower_additional_info_attributes)
        end

        context 'when borrower additional info record does not exist for the borrower' do
          before { borrower_additional_info.delete }

          it 'creates a new borrower additional info record' do
            call
            borrower_additional_info = BorrowerAdditionalInfo.last
            expect(borrower_additional_info).to have_attributes(new_borrower_additional_info_attributes)
          end
        end

        context 'when the borrower additional info record exists for another loan' do
          let(:expired_loan) { create(:loan, :expired, borrower_id: borrower.id) }

          before do
            borrower_additional_info.update(loan_id: expired_loan.id)
          end

          it 'creates a new borrower additional info record' do
            call
            borrower_additional_infos = BorrowerAdditionalInfo.where(borrower_id: borrower.id)
            expect(borrower_additional_infos.count).to eq(2)
            borrower_additional_info = borrower_additional_infos.where.not(loan_id: expired_loan.id).first
            expect(borrower_additional_info).to have_attributes(new_borrower_additional_info_attributes)
          end
        end

        it 'updates the loan record attributes' do
          loan.update(source_type: 'WEB')
          call
          loan.reload
          expect(loan.amount).to eq(loan_params[:amount])
          expect(loan.credit_score_range).to eq(loan_params[:credit_score_range])
          expect(loan.housing_status).to eq(borrower_params[:housing_status])
          expect(loan.time_at_residence).to eq(borrower_params[:time_at_residence])
          expect(loan.employment_status).to eq(borrower_params[:employment_status])
          expect(loan.anual_income).to eq(borrower_params[:income])
          expect(loan.education_level).to eq(borrower_params[:education_level])
          expect(loan.employment_pay_frecuency).to eq(borrower_params[:employment_pay_frecuency])
          expect(loan.monthly_housing_payment).to eq(borrower_params[:monthly_housing_payment])
          expect(loan.employment_start_date).to eq(borrower_params[:employment_start_date])
          expect(loan.employment_industry).to eq(borrower_params[:employment_industry])
          expect(loan.last_paycheck_on).to eq(borrower_params[:employment_last_pay_date].to_date)
          expect(loan.source_type).to eq('WEB')
        end
      end

      context 'when there is a loan with a matching code and request_id that is expired' do
        let!(:loan_app_status) { create(:loan_app_status, name: 'EXPIRED') }
        let!(:borrower) { create(:borrower) }
        let!(:loan) { create(:loan, borrower:, code:, request_id:, product_type: 'IPL', loan_app_status:) }
        it 'sets the status and response' do
          expect(call.status).to eq(405)
          expect(call.body).to eq(method_not_allowed_response('Cannot update loan in current status'))
        end
      end
    end

    describe '#borrower' do
      subject(:borrower) { call.borrower }

      let(:borrower_attributes) do
        {
          email: borrower_params[:email],
          status: 'verified',
          first_name: borrower_params[:first_name],
          last_name: borrower_params[:last_name],
          ssn: borrower_params[:ssn],
          date_of_birth: Date.strptime(borrower_params[:date_of_birth], '%m-%d-%Y'),
          identity_id: User.find_by(email:).id
        }
      end
      let(:borrower_additional_info_attributes) do
        {
          address_street: borrower_params[:address_street],
          address_apt: borrower_params[:address_apt],
          city: borrower_params[:city],
          state: borrower_params[:state],
          zip_code: borrower_params[:zip_code],
          phone_number: borrower_params[:phone_number]
        }
      end

      it 'creates a borrower with the right parameters' do
        expect(borrower).to have_attributes(borrower_attributes)
        expect(borrower.latest_borrower_info).to have_attributes(borrower_additional_info_attributes)
      end
    end

    describe '#loan' do
      subject(:loan) { call.loan }
      let(:loan_attributes) do
        {
          education_level: borrower_params[:education_level],
          credit_score_range: loan_params[:credit_score_range],
          product_type: loan_params[:product_type],
          code: loan_params[:code],
          amount: loan_params[:amount],
          housing_status: borrower_params[:housing_status],
          time_at_residence: borrower_params[:time_at_residence],
          employment_status: borrower_params[:employment_status],
          anual_income: borrower_params[:income],
          employment_start_date: borrower_params[:employment_start_date].to_s,
          employment_industry: borrower_params[:employment_industry],
          monthly_housing_payment: borrower_params[:monthly_housing_payment],
          employment_pay_frecuency: borrower_params[:employment_pay_frecuency],
          originating_party: 'DIRECT_LICENSES',
          program_id: lead.program_id,
          last_paycheck_on: borrower_params[:employment_last_pay_date].to_date
        }
      end
      let(:expected_loan_details) do
        loan_detail_attr_names = LoanDetail.attribute_names
        lead
          .loan_details
          .slice(*loan_detail_attr_names)
          .merge('beyond_enrollment_date' => Date.parse(lead.loan_details['beyond_enrollment_date']))
      end

      it 'creates a loan with the right parameters' do
        expect(loan).to have_attributes(loan_attributes)
        expect(loan.created_at).to be_within(5.seconds).of(Time.now)
        expect(loan.loan_payment_detail).to have_attributes(lead.payment_details.merge('beyond_payment_frequency' => 'monthly', 'beyond_payment_dates' => { 'dates' => {} }).merge('monthly_deposit_amount' => nil, 'estimated_payoff_amount' => nil))
        expect(loan.loan_payment_detail).not_to have_attribute(:payment_adherence_ratio_4_months)

        # force money fields to be a string for comparision
        loan_detail_attributes = loan.loan_detail.attributes

        %w[amount_financed estimated_beyond_fees estimated_cft_deposits months_since_enrollment payment_adherence_ratio_3_months payment_adherence_ratio_4_months payment_adherence_ratio_6_months total_amount_enrolled_debt eligibility_level].each do |key|
          loan_detail_attributes[key] = loan_detail_attributes[key].to_s
          expected_loan_details[key] = loan_detail_attributes[key].to_s
        end
        expect(loan_detail_attributes).to match(hash_including(expected_loan_details))

        expect(loan.loan_tradeline_details[0]).to have_attributes(lead.tradeline_details[0])
      end

      # TODO: Remove after Cleaning up Credit Model work
      context 'credit_model_store_payment_shock_fields is enabled', with_feature_flag: :credit_model_store_payment_shock_fields do
        before do
          loan_params.delete(:amount)
          loan_attributes.delete(:amount)
        end

        it 'creates loan_payment_detail when credit_model_store_payment_shock_fields is enabled' do
          expect(loan).to have_attributes(loan_attributes)
          expect(loan.amount).to eq(lead.payment_details['estimated_payoff_amount'])

          loan_payment_detail = loan.loan_payment_detail
          expect(loan_payment_detail.id).to be_present
          expect(loan_payment_detail.monthly_deposit_amount).to eq(lead.payment_details['monthly_deposit_amount'])
          expect(loan_payment_detail.estimated_payoff_amount).to eq(lead.payment_details['estimated_payoff_amount'])
        end
      end

      # TODO: Remove after Cleaning up Credit Model work
      it 'creates a new AboveLending LoanPaymentDetail record when payment_shock fields are missing' do
        lead.payment_details.merge!(monthly_deposit_amount: nil, estimated_payoff_amount: nil)
        lead.save
        expect(loan).to have_attributes(loan_attributes)

        loan_payment_detail = loan.loan_payment_detail
        expect(loan_payment_detail.id).to be_present
        expect(loan_payment_detail.monthly_deposit_amount).to be_nil
        expect(loan_payment_detail.estimated_payoff_amount).to be_nil
      end

      it 'updates program_duration_in_tmonths when it exists' do
        loan_attributes[:program_duration_in_tmonths] = lead.loan_details['program_duration_in_tmonths']
        expect(loan).to have_attributes(loan_attributes)
      end

      describe 'when crb template exists' do
        let!(:template) { create(:doc_template, type: 'CRB_INSTALLMENT_LOAN_AGREEMENT', states: [borrower_params[:state]]) }

        it 'sets the originating party to CRB' do
          expect(loan.originating_party).to eq('CRB')
        end
      end

      describe 'when loan creation date cannot be converted using Date.strptime' do
        let(:loan_created_at) { 1.year.ago }
        let(:loan_params) do
          {
            amount: Faker::Number.between(from: 30_000, to: 60_000),
            code:,
            product_type: 'IPL',
            credit_score_range: Loan.credit_score_ranges.keys.sample,
            loan_creation_date: loan_created_at
          }
        end

        it 'rescues the error and uses the original date format' do
          expect(loan).to have_attributes(loan_attributes)
        end
      end

      describe 'when lead has an identical tradeline' do
        it 'creates a loan_tradeline_detail for each entry' do
          lead_tradeline_count = lead.tradeline_details.count
          tradeline_detail = lead.tradeline_details.first
          lead.tradeline_details << tradeline_detail
          lead.save

          expect(loan.loan_tradeline_details.count).to eq(lead_tradeline_count + 1)
        end
      end
    end

    describe 'with existing borrower' do
      let!(:borrower) { create(:borrower, email:, identity_id: nil) }

      it 'still creates a new user' do
        call
        expect(Users::CreateUser).to have_received(:call)
      end

      it 'does not create a new borrower' do
        expect { call }.to_not change(Borrower, :count)
      end
    end

    describe 'with non-existing borrower' do
      let!(:borrower) { nil }
      let(:expected_params) do
        {
          first_name: borrower_params[:first_name],
          last_name: borrower_params[:last_name],
          email:,
          password: nil,
          service_entity_name: lead.service_entity_name,
          send_email: false
        }
      end
      let(:borrower_attributes) do
        {
          email: borrower_params[:email],
          status: 'verified',
          first_name: borrower_params[:first_name],
          last_name: borrower_params[:last_name],
          ssn: borrower_params[:ssn],
          date_of_birth: Date.strptime(borrower_params[:date_of_birth], '%m-%d-%Y'),
          identity_id: User.find_by(email:).id
        }
      end

      it 'creates a new user' do
        call
        expect(Users::CreateUser).to have_received(:call).with(expected_params)
      end

      it 'creates borrower' do
        expect { call }.to change(Borrower, :count).by(1)
        expect(Borrower.find_by(email:)).to have_attributes(borrower_attributes)
      end
    end

    describe 'tcpa_accepted' do
      context 'when tcpa_accepted is true' do
        before { borrower_params[:tcpa_accepted] = true }

        it 'updates the tcpa_accepted_at' do
          call
          expect(Borrower.find_by(email:).tcpa_accepted_at).to be_within(1.second).of(Time.zone.now)
        end
      end

      context 'when tcpa_accepted is false' do
        before { borrower_params[:tcpa_accepted] = false }

        context 'when the borrower already exists' do
          context 'when an existing borrower record already has tcpa_accepted_at attribute' do
            let!(:borrower) { create(:borrower, email:, tcpa_accepted_at: Faker::Time.backward) }

            it 'updates the borrower record tcpa_accepted_at to nil' do
              call
              expect(borrower.reload.tcpa_accepted_at).to be_nil
            end
          end

          context 'when borrower record does not have tcpa_accepted_at attribute' do
            let!(:borrower) { create(:borrower, email:, tcpa_accepted_at: nil) }

            it 'updates the borrower record tcpa_accepted_at to nil' do
              call
              expect(borrower.reload.tcpa_accepted_at).to be_nil
            end
          end
        end

        context 'when the borrower does not exist' do
          it 'does not update the tcpa_accepted_at' do
            call
            expect(Borrower.find_by(email:).tcpa_accepted_at).to be_nil
          end
        end
      end

      context 'when tcpa_accepted is not present' do
        let(:original_tcpa_accepted_at) { 1.day.ago }
        let!(:borrower) { create(:borrower, email:, tcpa_accepted_at: original_tcpa_accepted_at) }

        before { borrower_params.delete(:tcpa_accepted) }

        it 'does not change the tcpa_accepted_at value' do
          expect { call }.not_to(change { borrower.tcpa_accepted_at })
        end

        it 'updates other attributes without modifying tcpa_accepted_at' do
          call
          expect(borrower.tcpa_accepted_at).to be_within(1.second).of(original_tcpa_accepted_at)
        end
      end
    end

    describe "lead doesn't exist" do
      let!(:lead) { nil }

      it 'sets the right status and response' do
        expect(call.status).to eq(404)
        expect(call.body).to eq(not_found_response('Lead not found with that code'))
      end
    end

    describe 'borrower already has an active loan' do
      let!(:loan_app_status) { create(:loan_app_status, name: LoanAppStatus::ONGOING_LOAN_STATUSES.sample) }
      let!(:borrower) { create(:borrower, email:) }
      let!(:loan) { create(:loan, borrower:, product_type: 'IPL', loan_app_status:) }
      let!(:offer) { create(:offer, loan:, expiration_date: 2.days.from_now) }
      let(:message) { "Borrower email: #{borrower.email} already have an ongoing loan, loanId: #{loan.id}, unified_id: #{loan.unified_id}" }

      it 'sets the right status and response' do
        expect(call.status).to eq(405)
        expect(call.body).to eq(method_not_allowed_response(message))
      end

      describe 'with a possible expired_status but still valid' do
        let(:possibly_expired_status) { (LoanAppStatus::POSSIBLE_EXPIRED_STATUSES - LoanAppStatus::EXPIRED_STATUSES).sample }
        let!(:loan_app_status) { create(:loan_app_status, name: possibly_expired_status) }

        it 'sets the right status and response' do
          expect(call.status).to eq(405)
          expect(call.body).to eq(method_not_allowed_response(message))
        end
      end

      describe 'with a expirable status and expired offers' do
        let(:code) { loan.code }
        let(:possibly_expired_status) { (LoanAppStatus::POSSIBLE_EXPIRED_STATUSES - LoanAppStatus::ONGOING_LOAN_STATUSES).sample }
        let!(:loan_app_status) { create(:loan_app_status, name: possibly_expired_status) }
        let!(:offer) { create(:offer, loan:, expiration_date: 2.days.ago) }
        let(:message) { 'Cannot update loan in current status' }

        before do
          params.merge!(request_id: loan.request_id, code: loan.code)
          loan.update(loan_app_status_id: loan_app_status.id)
          allow(Loans::SyncStatusJob).to receive(:perform_async)
          allow(Loans::DeliverNoticeOfAdverseActionJob).to receive(:perform_async)
        end

        it 'updates the loan status to EXPIRED and enqueues jobs to sync with GDS and deliver NOAA' do
          service_object.call
          expect(loan.reload.loan_app_status.name).to eq('EXPIRED')
          expect(Loans::SyncStatusJob).to have_received(:perform_async).with(loan.id)
          expect(Loans::DeliverNoticeOfAdverseActionJob).to have_received(:perform_async).with(loan.id)
        end
      end

      describe 'with expired status' do
        let!(:loan_app_status) { create(:loan_app_status, name: 'EXPIRED') }
        let!(:new_lead) { create(:lead, code: loan.code, type: 'IPL', months_since_enrollment: 6) }

        it_behaves_like 'a successful request'
      end
    end

    describe 'ongoing loan using the same code' do
      let!(:loan_app_status) { create(:loan_app_status, name: 'OFFERED_WITH_DEBT_RELIEF') }
      let!(:loan) { create(:loan, code:, product_type: 'IPL', loan_app_status:) }
      let!(:offer) { create(:offer, loan:, expiration_date: 2.days.from_now) }
      let(:message) { "invitation code: #{code} already have an ongoing loan, loanId: #{loan.id}, unified_id: #{loan.unified_id}, borrower_email: #{loan.borrower.email}" }

      it 'and the status and response are set.' do
        expect(call.status).to eq(405)
        expect(call.body).to eq(method_not_allowed_response(message))
      end

      it 'sets the right status and response even when no offers are present on the loan' do
        loan.offers.destroy_all

        expect(call.status).to eq(405)
        expect(call.body).to eq(method_not_allowed_response(message))
      end

      describe 'with ongoing status' do
        let!(:loan_app_status) { create(:loan_app_status, name: LoanAppStatus::ONGOING_LOAN_STATUSES.sample) }

        before do
          params.merge!(request_id: loan.request_id, code: loan.code, email: loan.borrower.email)
        end

        it_behaves_like 'a successful request'
      end
    end

    describe 'when error occurs creating User record' do
      before do
        allow(Users::CreateUser).to receive(:call).and_raise('Boom!')
        call
      end

      it 'does not set the identity_id on the borrower record' do
        expect(Borrower.find_by(email:).identity_id).to be_nil
      end
    end

    context 'when experiment_2025_04_CHI_1753_Credit_Model_1_0 is active', with_feature_flag: :experiment_2025_04_CHI_1753_Credit_Model_1_0 do
      let!(:borrower) { create(:borrower, email:) }
      let(:experiment_cohort) { Experiment['2025_04_CHI_1753_Credit_Model_1_0'].cohort_for(borrower) }

      it 'returns body containing data related to experiment' do
        call
        expect(call.body).to include({ credit_test_1: experiment_cohort })
      end
    end
  end

  describe '#create_user' do
    subject(:new_user) { described_class.new(**params).send(:create_user) }

    let(:params) do
      {
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        code: '12345',
        product_type: 'IPL'
      }
    end
    let(:loan) { instance_double(Loan, id: 123) }

    before do
      allow_any_instance_of(described_class).to receive(:loan).and_return(loan)
      allow_any_instance_of(described_class).to receive(:lead).and_return(lead)
    end

    it 'calls CreateUser with the correct parameters' do
      expect(Users::CreateUser).to receive(:call).with(
        first_name: 'John',
        last_name: 'Doe',
        email: '<EMAIL>',
        password: nil,
        service_entity_name: 'Beyond Finance',
        send_email: false
      ).and_call_original

      expect(new_user).to be_a(User)
      expect(new_user.first_name).to eq(params[:first_name])
      expect(new_user.last_name).to eq(params[:last_name])
      expect(new_user.email).to eq(params[:email])
    end
  end
end
