# frozen_string_literal: true

require 'rails_helper'

RSpec.describe Clients::DashServicingApi do
  shared_examples 'dash servicing api' do
    it 'stores api_event with meta data' do
      subject
      event = expect_api_event_record(name: api_event_name)
      expect(event.metadata['borrower_id']).to eq(borrower.id)
    end

    it 'calls Auth::GenerateBorrowerTokens with correct borrower' do
      subject
      expect(Auth::GenerateBorrowerTokens).to have_received(:call).with(borrower:)
    end

    it 'calls Auth::GenerateExternalAppToken for servicing_dashboard' do
      subject
      expect(Auth::GenerateExternalAppToken).to have_received(:call)
        .with(client_id: external_app.client_id, client_secret: external_app.client_secret, grant_type: 'client_credentials')
    end
  end

  let(:config) { { base_url: 'http://example.com' } }
  let!(:external_app) { create(:external_app, name: 'servicing_dashboard') }
  let(:borrower) { build(:borrower) }
  let(:access_token) { SecureRandom.uuid }
  let(:oauth_token) { SecureRandom.uuid }
  let(:headers) do
    {
      'Content-Type' => 'application/json',
      'X-Authorization': access_token, # borrower token
      Authorization: "Bearer #{oauth_token}"
    }
  end

  before :each do
    Rails.cache.clear
    allow(Rails.application).to receive(:config_for).with(:dash_api).and_return(config)
    allow(Auth::GenerateBorrowerTokens).to receive(:call).and_return({ access_token: })
    allow(Auth::GenerateExternalAppToken).to receive(:call).and_return(oauth_token)
  end

  context '#dashboard-details' do
    subject { described_class.dashboard_details(borrower:) }
    let(:api_event_name) { 'dash_servicing_dashboard_details' }

    let(:payment_date) { 1.month.ago }
    let(:last_payment) do
      {
        payment_amount: 123.45,
        payment_principal: 23.45,
        payment_interest: 100,
        date: payment_date.iso8601
      }.stringify_keys
    end

    let(:response_body) do
      {
        'apr' => '27.1727',
        'loan_amount' => 12_388.04,
        'underwriting' => 1196.56,
        'loan_payment' => 351.09,
        'number_of_terms' => 71,
        'contract_date' => '2025-01-10',
        'current_due_date' => '2025-03-04',
        'current_payment_due' => '0.00',
        'days_past_due' => 0,
        'loan_status_text' => 'Open',
        'number_of_remaining_terms' => 71,
        'next_payment_amount' => '351.09',
        'next_payment_date' => '2025-03-04',
        'overdue_amount' => '0.00',
        'payment_frequency' => 'loan.frequency.monthly',
        'remaining_balance' => 12_902.45,
        'sub_status' => 'Good Standing',
        'sub_status_id' => 9,
        'last_payment' => last_payment,
        'borrower_name' => 'ERICA LAMBERT',
        'address' => '251 Will Circles apt 1',
        'city' => 'West Nicolefort',
        'state' => 'CA',
        'zip_code' => '94025',
        'debt_sale' => false,
        'beneficial_owner_name' => nil
      }
    end

    before :each do
      stub_request(:get, 'http://example.com/servicing/loan/dashboard-details')
        .with(headers:)
        .to_return(status: 200, body: response_body.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it_behaves_like 'dash servicing api'

    it 'returns a DashboardDetails object' do
      details = subject

      expect(details).to be_an_instance_of(Clients::DashServicingApi::DashboardDetails)

      expect(details.apr).to eq(response_body['apr'])
      expect(details.loan_amount).to eq(response_body['loan_amount'])
      expect(details.loan_payment).to eq(response_body['loan_payment'])
      expect(details.number_of_terms).to eq(response_body['number_of_terms'])
      expect(details.contract_date).to eq(Date.parse(response_body['contract_date']))
      expect(details.current_due_date).to eq(Date.parse(response_body['current_due_date']))
      expect(details.current_payment_due).to eq(response_body['current_payment_due'].to_f)
      expect(details.days_past_due).to eq(response_body['days_past_due'].to_f)
      expect(details.loan_status_text).to eq(response_body['loan_status_text'])
      expect(details.number_of_remaining_terms).to eq(response_body['number_of_remaining_terms'])
      expect(details.next_payment_amount).to eq(response_body['next_payment_amount'].to_f)
      expect(details.next_payment_date).to eq(Date.parse(response_body['next_payment_date']))
      expect(details.overdue_amount).to eq(response_body['overdue_amount'].to_f)
      expect(details.payment_frequency).to eq(response_body['payment_frequency'])
      expect(details.remaining_balance).to eq(response_body['remaining_balance'])
      expect(details.sub_status).to eq(response_body['sub_status'])
      expect(details.sub_status_id).to eq(response_body['sub_status_id'])
      expect(details.borrower_name).to eq(response_body['borrower_name'])
      expect(details.address).to eq(response_body['address'])
      expect(details.state).to eq(response_body['state'])
      expect(details.zip_code).to eq(response_body['zip_code'])
      expect(details.debt_sale?).to eq(response_body['debt_sale'])
      expect(details.beneficial_owner_name).to eq(response_body['beneficial_owner_name'])
      expect(details.initial_amount).to eq(response_body['loan_amount'] + response_body['underwriting'])

      expect(details.last_payment).to be_an_instance_of(Clients::DashServicingApi::LastPayment)
      expect(details.last_payment.payment_amount).to eq(last_payment['payment_amount'])
      expect(details.last_payment.payment_principal).to eq(last_payment['payment_principal'])
      expect(details.last_payment.payment_interest).to eq(last_payment['payment_interest'])
      expect(details.last_payment.date).to eq(DateTime.parse(last_payment['date']))
    end

    context 'faraday error' do
      before do
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:get, 'http://example.com/servicing/loan/dashboard-details')
          .to_raise(bad_request_error)
      end

      it 'raises an error when the request fails' do
        expect { subject }.to raise_error(Clients::DashServicingApi::Error)
      end

      it 'has proper error logging' do
        expect { subject }.to raise_error(Clients::DashServicingApi::Error) do |error|
          expect(error.wrapped_exception).to be_a(Faraday::Error)
          expect(error.message).to eq({ response_body: 'bad request error',
                                        response_status: 400,
                                        klass: 'Faraday::BadRequestError',
                                        message: 'bad request' })
        end

        expect_api_event_record(name: api_event_name)
      end
    end
  end

  context '#payment_history' do
    subject { described_class.payment_history(borrower:) }
    let(:api_event_name) { 'dash_servicing_payment_history' }

    let(:response_body) do
      { 'payments' => [
          { 'id' => 251_456,
            'amount' => '177.59',
            'date' => '2025-02-14T00:00:00+00:00',
            'type' => 'Unknown',
            'status' => 'success',
            'isCustomerInitiated' => false,
            'interest' => '118.37',
            'principal' => '59.22',
            'afterBalance' => '12952.07' },
          { 'id' => 241_967,
            'amount' => '177.59',
            'date' => '2025-01-02T00:00:00+00:00',
            'type' => 'Unknown',
            'status' => 'success',
            'isCustomerInitiated' => false,
            'interest' => '124.21',
            'principal' => '53.38',
            'afterBalance' => '13005.45' }
        ],
        'count' => 2,
        'loanpro_loan_id' => '144923' }
    end

    before :each do
      stub_request(:get, 'http://example.com/servicing/loan/payment-history')
        .with(headers:)
        .to_return(status: 200, body: response_body.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it_behaves_like 'dash servicing api'

    it 'returns a PaymentHistory object' do
      history = subject

      expect(history).to be_an_instance_of(Clients::DashServicingApi::PaymentHistory)
      expect(history.payments.first).to be_an_instance_of(Clients::DashServicingApi::Payment)
      expect(history.payments.first.id).to eq(251_456)
      expect(history.count).to eq(2)
      expect(history.loanpro_loan_id).to eq(144_923)
    end

    context 'faraday error' do
      before do
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:get, 'http://example.com/servicing/loan/payment-history')
          .to_raise(bad_request_error)
      end

      it 'raises an error when the request fails' do
        expect { subject }.to raise_error(Clients::DashServicingApi::Error)
      end

      it 'has proper error logging' do
        expect { subject }.to raise_error(Clients::DashServicingApi::Error) do |error|
          expect(error.wrapped_exception).to be_a(Faraday::Error)
          expect(error.message).to eq({ response_body: 'bad request error',
                                        response_status: 400,
                                        klass: 'Faraday::BadRequestError',
                                        message: 'bad request' })
        end

        expect_api_event_record(name: api_event_name)
      end
    end
  end

  describe '#upcoming_payments' do
    subject { described_class.upcoming_payments(borrower:) }
    let(:api_event_name) { 'dash_servicing_upcoming_payments' }

    let(:response_body) do
      [
        { 'id' => 283_615, 'amount' => '981.00', 'date' => '2025-03-06', 'type' => 'recurring', 'status' => 'pending' },
        { 'id' => 287_526, 'amount' => '1317.00', 'date' => '2025-03-18', 'type' => 'recurring', 'status' => 'pending' }
      ]
    end

    before :each do
      stub_request(:get, 'http://example.com/servicing/loan/upcoming-payments')
        .with(headers:)
        .to_return(status: 200, body: response_body.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it_behaves_like 'dash servicing api'

    it 'returns an array of payment objects' do
      payments = subject.payments
      expect(payments.count).to eq(2)

      payment = payments.first
      expect(payment).to be_an_instance_of(Clients::DashServicingApi::Payment)
      expect(payment.id).to eq(283_615)
      expect(payment.amount).to eq(981.00)
      expect(payment.date).to eq('2025-03-06'.to_date)
      expect(payment.type).to eq('recurring')
      expect(payment.status).to eq('pending')
    end

    context 'faraday error' do
      before do
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:get, 'http://example.com/servicing/loan/upcoming-payments')
          .to_raise(bad_request_error)
      end

      it 'raises an error when the request fails' do
        expect { subject }.to raise_error(Clients::DashServicingApi::Error)
      end

      it 'has proper error logging' do
        expect { subject }.to raise_error(Clients::DashServicingApi::Error) do |error|
          expect(error.wrapped_exception).to be_a(Faraday::Error)
          expect(error.message).to eq({ response_body: 'bad request error',
                                        response_status: 400,
                                        klass: 'Faraday::BadRequestError',
                                        message: 'bad request' })
        end

        expect_api_event_record(name: api_event_name)
      end
    end
  end

  describe '#payment_profiles' do
    subject { described_class.payment_profiles(borrower:) }
    let(:api_event_name) { 'dash_servicing_payment_profiles' }

    let(:response_body) do
      {
        'paymentProfiles' => [
          {
            'id' => 55_865,
            'isPrimary' => 1,
            'isSecondary' => 0,
            'title' => 'Personal Account ********',
            'type' => 'paymentAccount.type.checking',
            'checkingAccountId' => 55_873,
            'active' => 1,
            'visible' => 1,
            'bankName' => 'Wells Fargo',
            'accountNumber' => '3333',
            'routingNumber' => '*********'
          },
          {
            'id' => 55_867,
            'isPrimary' => 0,
            'isSecondary' => 1,
            'title' => 'Checking Account 1231234',
            'type' => 'paymentAccount.type.checking',
            'checkingAccountId' => 55_874,
            'active' => 0,
            'visible' => 1,
            'bankName' => 'Wells Fargo',
            'accountNumber' => '55555',
            'routingNumber' => '*********'
          }
        ]
      }
    end

    before :each do
      stub_request(:get, 'http://example.com/servicing/loan/payment_profiles')
        .with(headers:)
        .to_return(status: 200, body: response_body.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it_behaves_like 'dash servicing api'

    it 'returns a PaymentProfiles object' do
      profiles = subject.payment_profiles

      expect(profiles.count).to eq(2)
      expect(profiles.first).to be_an_instance_of(Clients::DashServicingApi::PaymentProfile)
      expect(profiles.last).to be_an_instance_of(Clients::DashServicingApi::PaymentProfile)

      expect(profiles.first.id).to eq(55_865)
    end

    context 'faraday error' do
      before do
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:get, 'http://example.com/servicing/loan/payment_profiles')
          .to_raise(bad_request_error)
      end

      it 'raises an error when the request fails' do
        expect { subject }.to raise_error(Clients::DashServicingApi::Error)
      end

      it 'has proper error logging' do
        expect { subject }.to raise_error(Clients::DashServicingApi::Error) do |error|
          expect(error.wrapped_exception).to be_a(Faraday::Error)
          expect(error.message).to eq({ response_body: 'bad request error',
                                        response_status: 400,
                                        klass: 'Faraday::BadRequestError',
                                        message: 'bad request' })
        end

        expect_api_event_record(name: api_event_name)
      end
    end
  end

  describe '#create_payment' do
    subject { described_class.create_payment(borrower:, body:) }
    let(:api_event_name) { 'dash_servicing_create_payment' }

    let(:date) { Date.today.iso8601 }
    let(:timezone) { 'UTC' }
    let(:amount) { Faker::Number.decimal(l_digits: 2, r_digits: 2) }
    let(:paymentProfileId) { rand(1..10) }
    let(:chargeOffRecovery) { false }

    # The create_payment body arguments
    let(:body) do
      {
        date:,
        timezone:,
        amount:,
        payment_profile_id: paymentProfileId,
        charge_off_recovery: chargeOffRecovery
      }
    end

    # :date, :timezone, :amount, :paymentProfileId, :chargeOffRecovery
    let(:payment_body) do
      {
        date:,
        timezone:,
        amount:,
        paymentProfileId:,
        chargeOffRecovery:
      }
    end

    let(:response_body) do
      { success: true, achPaymentNotificationEmailSent: 'OK' }
    end

    before :each do
      stub_request(:post, 'http://example.com/servicing/loan/payments')
        .with(headers:, body: payment_body.to_json)
        .to_return(status: 200, body: response_body.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it_behaves_like 'dash servicing api'

    it 'returns true when successful' do
      expect(subject).to be_truthy
    end

    context 'faraday error' do
      before do
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:post, 'http://example.com/servicing/loan/payments')
          .with(headers:, body: payment_body.to_json)
          .to_raise(bad_request_error)
      end

      it 'raises an error when the request fails' do
        expect { subject }.to raise_error(Clients::DashServicingApi::Error)
      end

      it 'has proper error logging' do
        expect { subject }.to raise_error(Clients::DashServicingApi::Error) do |error|
          expect(error.wrapped_exception).to be_a(Faraday::Error)
          expect(error.message).to eq({ response_body: 'bad request error',
                                        response_status: 400,
                                        klass: 'Faraday::BadRequestError',
                                        message: 'bad request' })
        end

        expect_api_event_record(name: api_event_name)
      end
    end
  end

  describe '#cancel_payment' do
    let(:payment_id) { rand(10_000..15_000) }
    subject { described_class.cancel_payment(borrower:, payment_id:) }
    let(:api_event_name) { 'dash_servicing_cancel_payment' }

    let(:response_body) { { success: true } }

    before :each do
      stub_request(:put, "http://example.com/servicing/loan/upcoming-payments/cancel/#{payment_id}")
        .with(headers:)
        .to_return(status: 200, body: response_body.to_json, headers: { 'Content-Type' => 'application/json' })
    end

    it_behaves_like 'dash servicing api'

    context 'when it succeeds' do
      it 'returns true' do
        expect(subject).to be_truthy
      end
    end

    context 'when it fails' do
      let(:response_body) { { success: false } }

      it 'returns false' do
        expect(subject).to be_falsey
      end
    end

    context 'faraday error' do
      before do
        response_hash = { status: 400, body: 'bad request error' }
        bad_request_error = Faraday::BadRequestError.new('bad request', response_hash)
        stub_request(:put, "http://example.com/servicing/loan/upcoming-payments/cancel/#{payment_id}")
          .with(headers:)
          .to_raise(bad_request_error)
      end

      it 'records the error' do
        subject

        event = expect_api_event_record(name: api_event_name)
        expect(event.metadata['status_code']).to eq(400)
        expect(event.response).to eq('bad request error')
      end

      it 'returns false' do
        expect(subject).to be_falsey
      end
    end
  end
end
