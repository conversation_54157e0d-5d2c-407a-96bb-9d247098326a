# frozen_string_literal: true

require_relative 'boot'

require 'rails/all'

# Require the gems listed in Gemfile, including any gems
# you've limited to :test, :development, or :production.
Bundler.require(*Rails.groups)

module ApplicationManagementSystem
  class Application < Rails::Application
    # Initialize configuration defaults for originally generated Rails version.
    config.load_defaults 7.0

    # Please, add to the `ignore` list any other `lib` subdirectories that do
    # not contain `.rb` files, or that should not be reloaded or eager loaded.
    # Common ones are `templates`, `generators`, or `middleware`, for example.
    config.autoload_lib(ignore: %w[assets tasks])

    # autoload paths
    config.autoload_paths << Rails.root.join('app/validators')
    config.autoload_paths << Rails.root.join('app/blueprints')
    config.autoload_paths << Rails.root.join('app/models/types')
    config.autoload_paths << Rails.root.join('lib')

    require 'usa_states'

    # Configuration for the application, engines, and railties goes here.
    #
    # These settings can be overridden in specific environments using the files
    # in config/environments, which are processed later.
    #
    # config.time_zone = "Central Time (US & Canada)"
    # config.eager_load_paths << Rails.root.join("extras")

    config.session_store :cookie_store, key: '_interslice_session', expire_after: 3.days
    config.middleware.insert 0, Rack::Sanitizer # can be removed once the this rails issue is resolved: https://github.com/rails/rails/issues/52114
    config.middleware.use Rack::MethodOverride
    config.middleware.use ActionDispatch::Cookies
    config.middleware.use ActionDispatch::Session::CookieStore
    config.middleware.use config.session_store, config.session_options
    config.middleware.use ActionDispatch::Flash

    # We don't want to wrap labels with a field_with_errors div as that breaks our layout
    config.action_view.field_error_proc = proc { |html_tag| html_tag }

    # use uuids by default as primary keys
    config.generators do |g|
      g.orm :active_record, primary_key_type: :uuid
    end

    # Configure the hostname at which this service will be exposed.
    ENV.fetch('DEVOPS_HOSTS', '').split.each do |host|
      # IP addresses like '*********/16' should be wrapped with `IPAddr`
      config.hosts << IPAddr.new(host)
    rescue IPAddr::InvalidAddressError
      # If wrapping failed - we treat it as a host name
      config.hosts << host
    end

    # Deployed environments use a clustered redis instance, so we must pass the
    # Redis::Cluster client. For local environments, we must use the Redis client.
    #
    # Clustered uri's use the 'rediss://` protocol
    if ENV['REDIS_URI']
      redis_instance = if ENV.fetch('REDIS_URI').start_with? 'rediss'
                         Redis::Cluster.new(nodes: ENV.fetch('REDIS_URI'))
                       else
                         Redis.new(url: ENV.fetch('REDIS_URI'))
                       end

      config.cache_store = :redis_cache_store, {
        redis: redis_instance,
        expires_in: 24.hours,
        namespace: ENV.fetch('REDIS_PREFIX', 'ams')
      }
    else
      # Use rails' memory store for caching
      config.cache_store = :memory_store
    end

    # Semantic Logging
    log_level = ENV.fetch('LOG_LEVEL', nil)&.upcase&.to_sym
    # needs to be downcase for semantic logger but upcase to check against constants
    config.log_level = Logger::Severity.constants.include?(log_level) ? log_level.downcase : :info
    config.semantic_logger.backtrace_level = :error
    config.rails_semantic_logger.add_file_appender = false

    # Setting message.named_tags in Semantic Logger, for more info see:
    # https://logger.rocketjob.io/rails.html
    #
    # New Relic has NewRelic::Agent.add_custom_log_attributes, but this is at a
    # system level and not individually.
    config.log_tags = {
      cf_ray: ->(request) { request.headers['CF-RAY'] },
      code: ->(request) { ::RequestTagging.abovelending_code(request) },
      loan_id: ->(request) { ::RequestTagging.abovelending_loan_id(request) },
      cc_request_id: ->(request) { ::RequestTagging.abovelending_cc_request_id(request) },
      unified_id: ->(request) { ::RequestTagging.abovelending_unified_id(request) },
      borrower_id: ->(request) { ::RequestTagging.abovelending_borrower_id(request) },
      product_type: ->(request) { ::RequestTagging.abovelending_product_type(request) },
      ip_address: ->(request) { ::RequestTagging.abovelending_ip_address(request) },
      request_id: :request_id
    }
  end
end
