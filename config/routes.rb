# frozen_string_literal: true

# Loan and <PERSON><PERSON><PERSON> Params from path parameters are logged through
# RequestTagging.
#
# Example:
# patch '/loan/:unified_id', to: 'loans#update'
#
# Using a different variation of code, loan_id, request_id,
# unified_id, borrower_id will likely not be tracked in NewRelic Logs without
# code changes
Rails.application.routes.draw do
  require 'sidekiq/pro/web'
  require 'sidekiq/cron/web'
  require 'sidekiq_unique_jobs/web'
  Sidekiq::Web.use(Rack::Auth::Basic) do |username, password|
    sidekiq_username = ENV.fetch('SIDEKIQ_DASHBOARD_USERNAME', '')
    sidekiq_password = ENV.fetch('SIDEKIQ_DASHBOARD_PASSWORD', '')

    ActiveSupport::SecurityUtils.secure_compare(username, sidekiq_username) &&
      ActiveSupport::SecurityUtils.secure_compare(password, sidekiq_password)
  end
  mount Sidekiq::Web => '/sidekiq'

  mount Rswag::Api::Engine => '/api-docs'
  mount Rswag::Ui::Engine => '/api-docs'

  mount LetterOpenerWeb::Engine, at: '/letter_opener' if Rails.env.development?

  mount Flipper::UI.app(Flipper.instance) => "/#{ENV.fetch('FLIPPER_UI_SECRET')}/flipper"

  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  resources :ping, only: :index

  # Legacy URLs
  get '/applynow', to: redirect(path: '/')
  get '/apply', to: redirect(path: '/')
  get '/ipl', to: redirect(path: '/')
  get '/offer', to: redirect(path: '/')
  get '/lander/documents/Consumer Information Brochure.pdf', to: redirect(path: '/documents/nm-consumer-brochure')
  # rubocop:disable Layout/LineLength
  get '/lander/documents/Above Lending-NM Small Loan License issued 02-28-24 expires 06-30-25.pdf', to: redirect(path: '/documents/nm-small-loan-license')
  # rubocop:enable Layout/LineLength

  resources :home, only: :index
  root 'home#index'

  resources :documents, only: :index do
    get :ada_accessibility_statement, on: :collection, path: 'ada-accessibility-statement'
    get :communication_consent, on: :collection, path: 'communication-consent'
    get :credit_profile_authorization, on: :collection, path: 'credit-profile-authorization'
    get :debt_collection_disclosures, on: :collection, path: 'debt-collection-disclosures'
    get :electronic_fund_transfer_authorization, on: :collection, path: 'electronic-fund-transfer-authorization'
    get :esign_consent, on: :collection, path: 'esign-consent'
    get :lending_partner_matching_consent, on: :collection, path: 'lending-partner-matching-consent'
    get :lending_partner_tcpa_consent, on: :collection, path: 'lending-partner-tcpa-consent'
    get :mi_regulatory_loan_license, on: :collection, path: 'mi-regulatory-loan-license'
    get :nm_consumer_brochure, on: :collection, path: 'nm-consumer-brochure'
    get :nm_small_loan_license, on: :collection, path: 'nm-small-loan-license'
    get :nm_loan_rate_fee_disclosure, on: :collection, path: 'nm-loan-rate-and-fee-disclosure'
    get :ny_credit_report_disclosure, on: :collection, path: 'ny-credit-report-disclosure'
    get :partners, on: :collection, path: 'partners'
    get :privacy_notices, on: :collection, path: 'privacy-notices'
    get :privacy_policy, on: :collection, path: 'privacy-policy'
    get :state_licenses, on: :collection, path: 'state-licenses'
    get :tcpa_consent, on: :collection, path: 'tcpa-consent'
    get :terms_of_use, on: :collection, path: 'terms-of-use'
  end

  namespace :api do
    unless Rails.env.production?
      # LandingLeadsController
      post '/landing_leads', to: 'landing_leads#create'

      # LoanApplicationController
      post '/application/pi1', to: 'loan_applications#pi1'
      post '/application/pi2', to: 'loan_applications#pi2'
      post '/application/select-offer', to: 'loan_applications#select_offer'

      # OffersController
      get '/offers/IPL/:loanId', to: 'offers#show', as: 'offers_show'

      # BankAccountsController
      post '/bank-accounts/', to: 'bank_accounts#create'
    end

    # AccountsController
    get '/accounts/by_email', to: 'accounts#find_by_email'
    get '/accounts/by_identity_id', to: 'accounts#find_by_identity_id'

    # BorrowerController
    patch '/borrower/:unified_id', to: 'borrowers#update', as: 'borrower_update'

    # CreditDisclosuresController
    get '/disclosures/credit', to: 'credit_disclosures#show'

    # LeadsController
    get '/leads/ipl/:code', to: 'leads#ipl', as: 'leads_ipl'
    post '/loan/ipl/:requestId/send-offers', to: 'loans#send_offers'

    # LoansController
    get '/loan', to: 'loans#show'
    post '/loan/apply', to: 'loans#upl_apply_for_loan'
    post '/loan/app-from-inquiry', to: 'loans#app_from_inquiry'
    get '/loan/inquiry/:loanInquiryId', to: 'loans#inquiry'
    post '/loan/ipl/app-by-phone', to: 'loans#app_by_phone'
    post '/loan/resend-onboarding-email', to: 'loans#resend_onboarding_email'
    post '/loan/ipl/send-disclosure', to: 'loans#send_disclosure'
    post '/loan/ipl/til/:loan_id/:token', to: 'loans#submit_til', as: 'submit_til', token: %r{[^/]+}
    patch '/loan/ipl/:loanId/store-signed-contracts', to: 'loans#store_signed_contracts'
    put '/loan/ipl/:requestId/final-decision', to: 'loans#final_decision', as: 'ipl_final_decision'
    get '/loan/:requestId/details', to: 'loans#details'
    put '/loan/:requestId/withdraw', to: 'loans#withdraw'
    post '/loan/dm2/bank-account/update', to: 'loans#update_bank_account'
    put '/loan/dm2/:requestId/final-decision', to: 'loans#upl_final_decision'
    post '/loan/dm2/til/:loan_id/:token', to: 'loans#upl_submit_til', as: 'upl_submit_til', token: %r{[^/]+}
    patch '/loan/:unified_id', to: 'loans#update'

    # OauthController
    post '/oauth/token', to: 'oauth#token'

    # OffersController
    post '/offers/ipl/generated', to: 'offers#generated'
    post '/offers/save-selection', to: 'offers#save_selection'

    # BankAccountsController
    post '/bank-accounts/add', to: 'bank_accounts#add'
    put '/bank-accounts/ipl/:requestId', to: 'bank_accounts#ipl'

    # TodosController
    post '/todos/sync-tasks', to: 'todos#sync_tasks'
    post '/todos/trigger-resync', to: 'todos#trigger_resync'
    get '/todos/documents/:documentId', to: 'todos#show_document'
    post '/todos/agent-upload-completed/:request_id', to: 'todos#agent_upload_completed'

    # UpdateFundingStatusController
    post '/webhooks/arix', to: 'arix_webhook#receive'

    # PlaidWebhooksController
    post '/webhooks/plaid', to: 'plaid_webhooks#receive'

    # TalkdeskEventsController
    post '/talkdesk_events', to: 'talkdesk_events#create'

    # OcrolusWebhooksController
    post '/webhooks/ocrolus', to: 'ocrolus_webhooks#receive'

    # LoanproWebhooksController
    post '/webhooks/loanpro', to: 'loanpro_webhooks#receive'

    # SendgridWebhooksController
    post '/webhooks/sendgrid', to: 'sendgrid_webhooks#receive'

    # SocureWebhooksController
    post '/webhooks/socure', to: 'socure_webhooks#receive'

    # UsersController
    post '/users/update_email', to: 'users#update_email'

    # Api::UtilsController
    post '/utils/cache_clear', to: 'utils#cache_clear'
    post '/utils/deliver_noaa', to: 'utils#deliver_noaa'
    post '/utils/force_loan_onboard', to: 'utils#force_loan_onboard'
    post '/utils/force_socure_monitoring', to: 'utils#force_socure_monitoring'
    post '/utils/force_plaid_asset_report', to: 'utils#force_plaid_asset_report'
    post '/utils/import_eligibility_files', to: 'utils#import_eligibility_files'
    post '/utils/sync_loan_status', to: 'utils#sync_loan_status'
    post '/utils/upload_report', to: 'utils#upload_report'
    get '/utils/generate_magic_link_token/:id', to: 'utils#generate_magic_link_token'
    post '/utils/generate_offers/:loan_id', to: 'utils#generate_offers'
    post '/utils/stamp_loan_agreement', to: 'utils#stamp_loan_agreement'

    post '/utils/generate_offer_code', to: 'utils#generate_offer_code' unless Rails.env.production?

    # Api::ArixOnboardingController
    namespace :arix_onboarding do
      post :trigger_initiation_job
      scope ':unified_id', constraints: { unified_id: /\d+/ } do
        get :onboarding_payloads
        post :loan_update
      end

      scope ':loan_id' do
        post :resubmit_documents
      end
    end

    # Api::ValidateEmailsController
    post '/validate_email', to: 'validate_emails#verify'
  end

  scope module: :borrowers, path: '' do
    resource :borrowers, path: '', only: [] do
      get :upl_account_new, controller: :accounts, path: 'create-account/:loan_inquiry_id'
      post :upl_account_create, controller: :accounts, path: 'create-account/:loan_inquiry_id'

      get :account_setup, controller: :accounts, path: 'graduation-loan/account-setup'
      post :resend_welcome_email, controller: :accounts, path: 'graduation-loan/account-setup'

      get :signin, controller: :sessions, path: 'signin'
      post :signin_create, controller: :sessions, path: 'signin'

      get :resume, controller: :sessions
      get :signout, controller: :sessions

      get :forgot_password, controller: :passwords, path: 'forgot-password'
      post :forgot_password_create, controller: :passwords, path: 'forgot-password'

      get :reset_password, controller: :passwords, path: 'reset-password'
      post :reset_password_create, controller: :passwords, path: 'reset-password'

      get :accept_invitation, controller: :passwords, path: 'accept-invitation'
      post :accept_invitation_create, controller: :passwords, path: 'accept-invitation'
    end
  end

  scope module: :loan_applications, path: 'graduation-loan' do
    resource :exit_pages, path: '', only: [] do
      collection do
        get :active_application, path: 'active-application'
        get :credit_freeze, path: 'credit-freeze'
        get :application_processing, path: 'application-processing'
        get :no_offer, path: 'no-offer'
        get :thank_you, path: 'thank-you'
        get :whoops
      end
    end

    resources :loan_applications, path: '', only: [] do
      collection do
        get '/', to: redirect(path: '/graduation-loan/intake-page-1')

        get :resume, controller: :intake

        get :intake, controller: :intake, path: 'intake-page-1'
        post :intake_create, controller: :intake, path: 'intake-page-1'

        get :basic_info, controller: :basic_info, path: 'basic-information-page-1'
        post :basic_info_create, controller: :basic_info, path: 'basic-information-page-1'

        get :additional_info, controller: :additional_info, path: 'additional-information-page-2'
        post :additional_info_create, controller: :additional_info, path: 'additional-information-page-2'

        post :credit_freeze_resubmit, controller: :credit_freeze

        get :select_offer, controller: :select_offer, path: 'select-offer'
        post :select_offer_create, controller: :select_offer, path: 'select-offer'
      end
    end
  end

  scope module: :loan_applications, path: '' do
    resources :loan_applications, path: 'reapply', only: [] do
      collection do
        get :reapply, controller: :reapply, path: ''
        post :reapply_create, controller: :reapply, path: ''

        get :continue, controller: :reapply, path: 'continue'
        post :continue_create, controller: :reapply, path: 'continue'

        get :continue_edit, controller: :reapply, path: 'continue/edit'
        post :continue_save, controller: :reapply, path: 'continue/edit'
      end
    end
  end

  scope module: :verification_documents, path: '' do
    resources :bank_accounts, path: 'loan-app/bank-account', only: [:index] do
      collection do
        post :token_save

        get :select, path: 'select'
        post :select_save, path: 'select'

        get :manual, path: 'manual'
        post :manual_create, path: 'manual'
      end
    end

    resources :todos, path: 'loan-app/todo', only: %i[index show] do
      collection do
        get :list
      end

      member do
        post :document_create
      end
    end

    resources :contracts, path: '', only: [] do
      collection do
        get :contract_auth, path: 'loan-app/contract/:token'

        get :index, path: 'loan-app/til'
        get :contract, path: 'loan-app/til/contract'

        get :sign_completed, path: 'sign-completed(/:token)'
        get :congratulations, path: 'loan-app/congratulations'
      end
    end

    resources :adverse_actions, path: 'loan-app/aa', only: [:index]
  end

  scope module: :servicing, as: :servicing, path: 'dashboard' do
    resources :dashboard, path: '', only: [:index] do
      get :whoops, on: :collection
    end

    resources :documents, path: 'documents', only: [:show]

    resources :payments, path: 'payments', except: %i[edit update show] do
      get :success, on: :collection
    end

    get '/schedule-payment', to: redirect(path: '/dashboard/payments/new')
    get '/payment-history', to: redirect(path: '/dashboard/payments')
  end

  get '/.well-known/openid-configuration', to: 'openid#openid_configuration'
  get '/.well-known/jwks.json', to: 'openid#jwks'

  get '/maintenance', to: 'static#maintenance', as: 'maintenance_page'

  # ::UtilsController
  get '/utils/download_envelope/:id', to: 'utils#download_envelope', as: 'download_envelope'

  get '/utils/generate_offer_code', to: 'utils#new_generate_offer_code' unless Rails.env.production?

  namespace :admin do
    get '/borrowers/', to: 'borrowers#index'
    post '/borrowers/search', to: 'borrowers#search'

    get '/leads/', to: 'leads#index'
    post '/leads/search', to: 'leads#search'
    get '/leads/show/:lead_id', to: 'leads#show', as: 'leads_show'

    get '/noaas', to: 'noaas#index'
    post '/noaas/search', to: 'noaas#search'

    get '/income_calculator', to: 'income_calculator#index'
    post '/income_calculator/run', to: 'income_calculator#run'

    get '/email_resend', to: 'email_resend#index'
    post '/email_resend/run', to: 'email_resend#run'

    root to: 'dashboards#index'
  end

  # Catch-all route to handle undefined routes
  match '*path', to: 'application#route_not_found', via: :all
end
