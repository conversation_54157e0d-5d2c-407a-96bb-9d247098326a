# frozen_string_literal: true

module EligibilityFileImporter
  class BatchCallback
    def on_success(status, options)
      Rails.logger.info('Eligibility file import completed successfully', status:, options:)
    end

    def on_c
      Rails.logger.error('Eligibility file import failed', job:, exception:)
    end
  end

  class Coordinator < ApplicationJob
    sidekiq_options queue: 'default', tags: %w[eligibility_file_importer coordinator], retry: 10

    # Number of lines to process per worker batch
    # The actual CSV processing batch size is handled by the Worker class
    LINES_PER_WORKER_BATCH = 5

    class FileNotFound < StandardError; end

    def perform(target_date = Time.zone.now.to_date)
      @target_date = target_date

      Rails.logger.info("#{self.class}: Starting eligibility file import for target date #{@target_date}")

      file_path = retrieve_eligibility_file
      file_metrics = calculate_file_metrics(file_path)
      log_file_metrics(file_metrics)

      batch_process_eligibility_file(file_path, file_metrics[:line_count])

      Rails.logger.info("#{self.class}: Successfully initiated batch processing")
    rescue StandardError => e
      Rails.logger.error("#{self.class}: Failed to process eligibility file", error: e.message)
      raise
    end

    private

    attr_reader :target_date

    def retrieve_eligibility_file
      Rails.logger.info("#{self.class}: Retrieving eligibility file for target date #{target_date}")

      file_path = Clients::BeyondEligibilityFiles.retrieve(target_date)

      if file_path.blank?
        raise FileNotFound, "#{self.class}:Failed to retrieve eligibility file for target date #{target_date}"
      end

      file_path
    end

    def calculate_file_metrics(file_path)
      {
        file_size_kb: (File.size(file_path) / 1024.0).round(2),
        line_count: File.foreach(file_path).count,
        checksum: Digest::MD5.hexdigest(File.read(file_path))
      }
    end

    def log_file_metrics(metrics)
      Rails.logger.info("#{self.class}: File metrics", file_size_kb: metrics[:file_size_kb],
                                                       line_count: metrics[:line_count], checksum: metrics[:checksum])
    end

    def batch_process_eligibility_file(file_path, line_count)
      batch = Sidekiq::Batch.new
      batch.description = "Importing eligibility file for target date #{target_date}"

      # Calculate number of worker batches needed
      # Subtract 1 from line_count to account for CSV header row
      batch_count = (line_count.to_f / LINES_PER_WORKER_BATCH).ceil
      Rails.logger.info("#{self.class}: Creating #{batch_count} worker batches for #{line_count} data lines")

      batch.jobs do
        Worker.perform_bulk(
          batch_count.times.map { |batch_index| [target_date, file_path, batch_index] }
        )
      end

      batch.on(:success, BatchCallback.new)
    end
  end
end
