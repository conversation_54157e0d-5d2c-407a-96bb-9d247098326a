<%= render UI::CardComponent.new do |c| %>
  <% c.with_body do %>
    <div data-testid="loan-details" class="max-w-2xl">
      <p class="font-light mb-4">Loan ID: <b class="font-bold"><%= unified_id %></b></p>

      <div class="border border-brand-gray-300 bg-gray-50 rounded-lg p-4 md:p-6 mb-4">
        <p class="mb-4 font-bold">Amount Due</p>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="flex flex-col gap-4">
            <div data-testid="next-payment-due-amount" class="text-5xl font-semibold">
              <%= number_to_currency(amount_due) %>
            </div>

            <% if render_due_date? %>
              <p class="mb-0 text-sm"><b>Payment Due:</b> <%= due_date.strftime('%b %d, %Y') %></p>
            <% end %>
          </div>

          <div class="flex flex-col gap-4">
            <% if render_make_payment? %>
              <%= render UI::ButtonComponent.new(href: servicing_schedule_payment_path,
                                                 testid: 'make-payment-button').with_content('Make a Payment') %>
            <% end %>

            <div class="flex gap-4">
              <p class="my-auto text-sm font-bold">
                AutoPay:
                <% if autopay_active %>
                  <span data-testid="payment-type-auto-pay">Active</span>
                <% else %>
                  <span data-testid="payment-type-manual" class="text-red-700">Inactive</span>
                <% end %>
              </p>

              <% unless autopay_active %>
                <%= render UI::ModalComponent.new(title: 'Set up AutoPay', testid: 'setup-autopay-modal') do |c| %>
                  <%= c.with_trigger do %>
                    <button class="text-brand-blue-500 hover:text-brand-blue-800 cursor-pointer text-sm underline">Manage AutoPay</button>
                  <% end %>

                  <%= c.with_body do %>
                    <div class="max-w-lg mx-auto text-center">
                      <p class="font-light mb-2">
                        You are currently not enrolled in AutoPay so you must make manual
                        payments by mail, phone, or online.
                      </p>

                      <p class="font-light mb-8">
                        With AutoPay, your payments will be automatically withdrawn on the
                        date(s) as scheduled.
                      </p>

                      <p class="text-2xl mb-6 font-semibold">
                          Call us
                          <%= service_entity_phone_number(
                                service_entity,
                                class: 'text-brand-blue-500 hover:text-brand-blue-800 underline'
                              ) %>
                      </p>

                      <p class="font-light mb-8">
                        Our loan specialists are available Monday through Friday, 8:00 am to
                        7:00 pm Central Time. You can also reach us by email at
                        <a href="mailto:<EMAIL>" class="text-brand-blue-500 hover:text-brand-blue-800 underline"><EMAIL></a>.
                      </p>
                    </div>
                  <% end %>

                <% end %>
              <% end %>
            </div>
          </div>
        </div>
      </div>

      <div data-controller="toggle">
        <div class="hidden mb-4" data-toggle-target="content">
          <div class="grid sm:grid-cols-2 gap-4">
            <% if payment_frequency.present? %>
              <div data-testid="payment-frequency">
                <p class="mb-0 font-medium"><%= payment_frequency %></p>
                <p class="text-xs">Payment Frequency</p>
              </div>
            <% end %>

            <% unless charged_off %>
              <div data-testid="remaining-payments">
                <p class="mb-0 font-medium"><%= remaining_payments %></p>
                <p class="text-xs">Remaining Payments</p>
              </div>
            <% end %>

            <div data-testid="initial-loan-amount">
              <p class="mb-0 font-medium"><%= number_to_currency(initial_amount) %></p>
              <p class="text-xs">Initial Loan Amount</p>
            </div>

            <div data-testid="apr">
              <p class="mb-0 font-medium"><%= number_to_percentage(apr, precision: 2, format: '%n %') %></p>
              <p class="text-xs">APR</p>
            </div>

            <div data-testid="remaining-balance">
              <p class="mb-0 font-medium"><%= number_to_currency(remaining_balance) %></p>
              <p class="text-xs">Remaining Balance</p>
            </div>
          </div>
        </div>

        <div data-toggle-target="show">
          <button class="text-brand-blue-500 hover:text-brand-blue-800 text-sm underline" data-action="click->toggle#show">
            View Additional Loan Information
          </button>
        </div>

        <div class="hidden" data-toggle-target="hide">
          <button class="text-brand-blue-500 hover:text-brand-blue-800 text-sm underline" data-action="click->toggle#hide">
            Hide Additional Loan Information
          </button>
        </div>
      </div>
    </div>
  <% end %>
<% end %>
