# frozen_string_literal: true

module Servicing
  module Payments
    class PaymentAmountFieldComponent < ApplicationComponent
      attr_reader :form

      def initialize(form:)
        @form = form

        super
      end

      def past_due_amount
        form.object.overdue_amount
      end

      def last_payment_amount
        form.object.last_payment_amount.to_f
      end

      def payoff_amount
        form.object.remaining_balance
      end

      def render_past_due?
        form.object.is_past_due
      end

      def render_last_payment?
        !render_past_due? && last_payment_amount.positive? && last_payment_amount != form.object.next_payment_amount
      end

      def today
        Date.today.strftime('%m/%d/%Y')
      end
    end
  end
end
