# frozen_string_literal: true

# == Schema Information
#
# Table name: doc_templates
#
#  id               :uuid             not null, primary key
#  body             :text
#  deleted_at       :timestamptz
#  doc_content_type :text             default("html")
#  name             :string(200)      not null
#  states           :string(2)        is an Array
#  type             :string(50)       not null
#  uri              :string(255)
#  version          :integer          not null
#  created_at       :timestamptz      not null
#  updated_at       :timestamptz
#
# Indexes
#
#  doc_templates_null_state                  (name, version, ((states IS NULL))) UNIQUE WHERE (states IS NULL)
#  doc_templates_states_index                (states) USING gin
#  doc_templates_version_type_states_unique  (version,type,states) UNIQUE
#
class DocTemplate < ApplicationRecord
  self.inheritance_column = nil

  TYPES = {
    AA: 'AA',
    ACH_AUTHORIZATION_SINGLE_PAYMENT: 'ACH_AUTHORIZATION_SINGLE_PAYMENT',
    ACH_AUTHORIZATION_RECURRING_PAYMENT: 'ACH_AUTHORIZATION_RECURRING_PAYMENT',
    ARBITRATION_AGREEMENT: 'ARBITRATION_AGREEMENT', # not in staging/prod DB
    CHARGE_OFF_NOTICE: 'CHARGE_OFF_NOTICE',
    COMMUNICATION_CONSENT_POLICY: 'COMMUNICATION_CONSENT_POLICY',
    CONCENT_AUTOMATED_CALLS: 'CONCENT_AUTOMATED_CALLS',
    CRB_AA: 'CRB_AA',
    CRB_INSTALLMENT_LOAN_AGREEMENT: 'CRB_INSTALLMENT_LOAN_AGREEMENT',
    CRB_TIL: 'CRB_TIL',
    CREDIT_PROFILE_AUTHORIZATION: 'CREDIT_PROFILE_AUTHORIZATION',
    CREDIT_SCORE_DISCLOSURE_NOTICE: 'CREDIT_SCORE_DISCLOSURE_NOTICE',
    CREDIT_SERVICES_CONTRACT_MARYLAND: 'CREDIT_SERVICES_CONTRACT_MARYLAND',
    DEBT_VALIDATION: 'DEBT_VALIDATION',
    DM_CRB_INSTALLMENT_LOAN_AGREEMENT: 'DM_CRB_INSTALLMENT_LOAN_AGREEMENT',
    DM_CRB_TIL: 'DM_CRB_TIL',
    DM_CREDIT_SERVICES_CONTRACT_MARYLAND: 'DM_CREDIT_SERVICES_CONTRACT_MARYLAND',
    DM_DL_INSTALLMENT_LOAN_AGREEMENT: 'DM_DL_INSTALLMENT_LOAN_AGREEMENT',
    DM_DL_TIL: 'DM_DL_TIL',
    DM_NOTICE_OF_CANCELLATION_MARYLAND: 'DM_NOTICE_OF_CANCELLATION_MARYLAND',
    ELECTRONIC_FUNDS_TRANSFER_AUTHORIZATION: 'ELECTRONIC_FUNDS_TRANSFER_AUTHORIZATION', # not in staging/prod
    ELECTRONIC_FUND_TRANSFER_AUTH: 'ELECTRONIC_FUND_TRANSFER_AUTH',
    ESIGN_ACT_CONSENT: 'ESIGN_ACT_CONSENT',
    INSTALLMENT_LOAN_AGREEMENT: 'INSTALLMENT_LOAN_AGREEMENT',
    LENDER_NETWORK_MATCHING_CONSENT: 'LENDER_NETWORK_MATCHING_CONSENT',
    LENDER_NETWORK_TCPA_CONSENT: 'LENDER_NETWORK_TCPA_CONSENT',
    NOTICE_OF_CANCELLATION_MARYLAND: 'NOTICE_OF_CANCELLATION_MARYLAND',
    NOTICE_OF_CORRECTION_TO_ITEMIZATION_OF_AMOUNT_FIN: 'NOTICE_OF_CORRECTION_TO_ITEMIZATION_OF_AMOUNT_FIN',
    NOTICE_OF_DEFAULT_KS: 'NOTICE_OF_DEFAULT_KS',
    NOTICE_OF_DEFAULT_MO: 'NOTICE_OF_DEFAULT_MO',
    NOTICE_OF_DEFAULT_WI: 'NOTICE_OF_DEFAULT_WI',
    NOTICE_OF_INCOMPLETE_APPLICATION: 'NOTICE_OF_INCOMPLETE_APPLICATION',
    # The Privacy Notice is an annual notification delivered to all active borrowers.
    PRIVACY_NOTICE: 'PRIVACY_NOTICE',
    # The Privacy Policy is a version of the associated policy published on our website captured one time when the user agrees to this policy.
    PRIVACY_POLICY: 'PRIVACY_POLICY',
    STATE_LICENSE: 'STATE_LICENSE',
    STATEMENT_OF_RIGHTS_DC: 'STATEMENT_OF_RIGHTS_DC',
    TERMS_OF_USE: 'TERMS_OF_USE',
    TIL: 'TIL',
    STAMPED_LOAN_AGREEMENT: 'STAMPED_LOAN_AGREEMENT',
    PLAID_ASSET_REPORT: 'PLAID_ASSET_REPORT'
  }.freeze

  ELECTRONIC_FUND_TRANSFER_AUTH_DOCUMENTS = TYPES.values_at(*%i[ELECTRONIC_FUND_TRANSFER_AUTH]).freeze
  WBO_CONSENT_DOCUMENTS = TYPES.values_at(
    *%i[
      ESIGN_ACT_CONSENT
      PRIVACY_POLICY
      TERMS_OF_USE
      CREDIT_PROFILE_AUTHORIZATION
    ]
  ).freeze
  UPL_CONSENT_DOCUMENTS = TYPES.values_at(
    *%i[
      CREDIT_PROFILE_AUTHORIZATION
      ESIGN_ACT_CONSENT
      PRIVACY_POLICY
      TERMS_OF_USE
    ]
  ).freeze
  VALID_IPL_SIGNED_DOCUMENT_TYPES = [
    TYPES[:TERMS_OF_USE],
    TYPES[:PRIVACY_POLICY],
    TYPES[:ESIGN_ACT_CONSENT],
    TYPES[:CREDIT_PROFILE_AUTHORIZATION],
    TYPES[:ELECTRONIC_FUNDS_TRANSFER_AUTHORIZATION]
  ].freeze
  VALID_SIGNED_DOCUMENT_TYPES = [
    TYPES[:TERMS_OF_USE],
    TYPES[:PRIVACY_POLICY],
    TYPES[:ESIGN_ACT_CONSENT],
    TYPES[:COMMUNICATION_CONSENT_POLICY],
    TYPES[:CREDIT_PROFILE_AUTHORIZATION],
    TYPES[:LENDER_NETWORK_MATCHING_CONSENT],
    TYPES[:LENDER_NETWORK_TCPA_CONSENT]
  ].freeze
  VALID_STAMPABLE_AGREEMENT_TYPES = [
    TYPES[:CRB_INSTALLMENT_LOAN_AGREEMENT],
    TYPES[:DM_CRB_INSTALLMENT_LOAN_AGREEMENT],
    TYPES[:DM_DL_INSTALLMENT_LOAN_AGREEMENT],
    TYPES[:INSTALLMENT_LOAN_AGREEMENT],
    TYPES[:CRB_TIL],
    TYPES[:DM_CRB_TIL],
    TYPES[:DM_DL_TIL],
    TYPES[:TIL]
  ].freeze

  CONSENT_DOCUMENT_TYPES = Set.new
                              .merge(UPL_CONSENT_DOCUMENTS)
                              .merge(ELECTRONIC_FUND_TRANSFER_AUTH_DOCUMENTS)
                              .freeze

  enum :type, TYPES

  def self.consent_document_template?(type)
    CONSENT_DOCUMENT_TYPES.include?(type)
  end

  def self.latest_version(type:)
    DocTemplate.where(type:)
               .order(version: :desc)
               .first
  end
end
