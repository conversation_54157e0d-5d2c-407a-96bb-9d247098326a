# frozen_string_literal: true

module VerificationDocuments
  class ManualBankAccountFormModel < ApplicationFormModel
    attribute :account_number
    attribute :account_number_confirmation
    attribute :account_type, default: 'checking'
    attribute :routing_number
    attribute :bank_name
    attribute :bank_account_authorization, :boolean
    attribute :first_name
    attribute :last_name
    attribute :modal_fund_transfer_authorization, :boolean
    attribute :non_modal_fund_transfer_authorization, :boolean
    attribute :override_fund_transfer_authorize, :boolean
    attribute :loan_id

    validates :first_name, presence: { message: 'Account holder first name is required' },
                           format: { with: /\A^([A-Za-z][ '-]?)+$\z/,
                                     message: 'Please enter a valid first name' }

    validates :last_name, presence: { message: 'Account holder last name is required' },
                          format: { with: /\A^([A-Za-z][ '-]?)+$\z/,
                                    message: 'Please enter a valid last name' }

    validates :account_number,
              presence: { message: 'Please enter an account number' },
              numericality: { only_integer: true, message: 'Account number must be a number' },
              length: { minimum: 5, too_short: 'Account number must be at least 5 digits',
                        maximum: 17, too_long: 'Account number must be at most 17 digits' }

    validates :account_number_confirmation,
              presence: { message: 'Please confirm account number' },
              numericality: { only_integer: true, message: 'Account number must be a number' },
              length: { minimum: 5, too_short: 'Account number must be at least 5 digits',
                        maximum: 17, too_long: 'Account number must be at most 17 digits' }

    validate :ensure_account_number_matches

    validates :routing_number,
              presence: { message: 'Routing number is required' },
              numericality: { only_integer: true, message: 'Routing number must be a number' },
              length: { is: 9, message: 'Routing number must be exactly 9 digits' }

    validates :account_type, inclusion: { in: %w[checking savings], message: 'Please select an account type' }
    validates :bank_name, presence: { message: 'Please enter bank name' }
    validates :bank_account_authorization, presence: { message: 'This field is required' }

    def manual_bank_account_attributes
      {
        account_number:,
        account_type:,
        bank_name:,
        first_name:,
        last_name:,
        loanId: loan_id,
        fund_transfer_authorize:,
        routing_number:
      }
    end

    def show_auto_pay_confirmation?
      valid? && !fund_transfer_authorize && !override_fund_transfer_authorize
    end

    def ensure_account_number_matches
      return if account_number.present? && account_number == account_number_confirmation

      errors.add(:account_number_confirmation, 'Account numbers must match')
    end

    private

    # NOTE:  This property comes from checking the autopay checkbox in the primary form OR the confirmation modal
    def fund_transfer_authorize
      non_modal_fund_transfer_authorization || modal_fund_transfer_authorization
    end
  end
end
