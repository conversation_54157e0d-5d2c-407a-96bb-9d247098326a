# frozen_string_literal: true

module LoanApplications
  class RecordRequestEvent < Service::Base
    attribute :event_agent
    attribute :request_event_name
    attribute :request
    attribute :resolver
    attribute :response
    attribute :meta
    attribute :form_model, type_for(ApplicationFormModel), optional: true

    def call
      # NOTE:  This refers to app/services/record_request_event
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta:, form_model:)
    end

    private

    def meta
      return { agent: event_agent }.merge(super.to_h) if resolver.code.blank?

      {
        agent: event_agent,
        lead_code: resolver.code,
        landing_lead_id: resolver.landing_lead_id,
        is_eligible: resolver.lead_eligible?,
        lead_id: resolver.lead_id,
        loan_id: resolver.loan_id,
        is_loan_active: resolver.loan_active?,
        borrower_id: resolver.borrower_id,
        is_activated_account: resolver.activated_account?,
        is_loan_present: resolver.loan_id.present?,
        is_basic_info_complete_status: resolver.basic_info_complete?,
        is_add_info_complete_status: resolver.add_info_complete?,
        is_withdraw_loan: resolver.withdraw_loan?
      }.merge(super.to_h)
    end
  end
end
