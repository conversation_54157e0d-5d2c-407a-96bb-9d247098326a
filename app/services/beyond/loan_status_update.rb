# frozen_string_literal: true

module Beyond
  # The api documention from Beyond, is found at the following.
  # https://docs.google.com/document/d/1TqePxuqF1GQyPfNqni9euGnjOrvoTHukbU4QQ2iEYDA/edit
  class LoanStatusUpdate < Service::Base
    include ActionView::Helpers::NumberHelper
    include DateHelper

    attribute :loan, type_for(::Loan)
    attribute :loan_app_status, :string
    attribute :updated_at, type_for(Time)

    validates :loan, presence: true
    validates :loan_app_status, presence: true
    validates :updated_at, presence: true

    # NOTE: Coordinate with Beyond before updating the reasons array.
    #       The Beyond API will raise an error if new reasons are sent without prior registration.
    BACKEND_DECLINE_REASONS = [
      'Financial institution account does not meet minimum requirements', # clean up
      'Bank account/routing information does not match information provided at ' \
      'application or is invalid or recent overdraft/insufficient funds activity in the bank account',
      'No longer meets eligibility requirements', # clean up
      'Not a citizen or permanent resident of United States', # clean up
      'Not a citizen or permanent resident of the United States',
      'Unable to verify identity',
      'Unable to verify income',
      'Unable to verify residence',
      'Verification documents not submitted' # clean up
    ].freeze

    FRONTEND_DECLINE_REASONS = [
      'Bankruptcy', # clean up
      'Bankruptcy filing within the last 7 years',
      'Credit bureau file not returned',
      'Debt to income ratio',
      'Income requirements not met', # clean up
      'Length of credit history',
      'Low FICO score',
      'Number of accounts with recent delinquency', # clean up
      'Recent payment delinquency on another loan',
      'Number of payment deferral programs', # clean up
      'Number of recent inquiries', # clean up
      'Number of recent credit inquiries',
      'Number of recently opened tradelines', # clean up
      'Number of recently opened loans is too high',
      'Repossession or foreclosure',
      'Unable to pull credit',
      'Unable to verify personal information', # clean up
      'Applicant identity does not match credit profile',
      'Loan to income ratio',
      'High credit utilization ratio',
      'Payment to income ratio is too high',
      'Loan payment amount exceeds affordability criteria',
      "Above Lending  does not operate in applicant's state of residence",
      "Loan amount is outside the minimum/maximum that Above Lending offers in applicant's state",
      'Recent loan application declined; ineligible to re-apply for at least 90 days',
      'Prior loan obtained through Above Lending; ineligible to re-apply',
      'Ineligible due to previously declined Above Lending Application'
    ].freeze

    DEFAULT_FED = 'Number of payment deferral programs'

    # Sends requests to Beyond, when new_status matches their expectations
    def call
      validate!
      return if lender_status.blank?
      return if loan.program_id.nil?
      return unless loan.ipl?

      @offer = loan.selected_offer

      Rails.logger.info("ams_hook_loan_status_update_with_params #{inputs}")
      Clients::BeyondApi.call(loan.program_id, build_body.compact)
    end

    private

    attr_reader :offer

    def inputs
      @inputs ||= {
        body: build_body.compact,
        params: {
          programId: loan.program_id
        }
      }
    end

    # Fetches all possible body values, values can be nil and filtered out
    # before sending to Beyond API
    def build_body # rubocop:disable Metrics
      {
        lender_loan_id: loan.id,
        lender_status:,
        back_end_decline_reason: BACKEND_DECLINE_REASONS.find { |reason| reason == loan.decline_reason_text },
        front_end_decline_reason: fed_reason,
        funded_amount: format_currency(offer&.amount),
        financed_amount: format_currency(offer&.amount_financed),
        cashback_amount: format_currency(offer&.cashout_amount),
        origination_fee: format_currency(offer&.origination_fee),
        funded_date: DateHelper.format_datetime(find_status_updated_date('IPL_FUNDED')),
        approval_date: DateHelper.format_datetime(find_status_updated_date('IPL_APPROVED')),
        application_date: DateHelper.format_datetime(loan.created_at),
        lender_status_updated_date_time: DateHelper.format_datetime(lender_status_updated_datetime)
      }
    end

    def fed_reason
      return unless loan.front_end_declined?

      FRONTEND_DECLINE_REASONS.find { |reason| reason == loan.decline_reason_text } ||
        DEFAULT_FED
    end

    def lender_status
      LoanAppStatus::ABOVE_TO_BEYOND_LOAN_STATUSES[loan.beyond_status_name]
    end

    def find_status_updated_date(name)
      loan.loan_status_histories.where(new_status: name).first&.updated_at
    end

    def lender_status_updated_datetime
      find_status_updated_date("#{loan.product_type}_#{loan_app_status.upcase}") || Time.current
    end

    def format_currency(number)
      number_to_currency(number, unit: '', separator: '.', delimiter: '')
    end
  end
end
