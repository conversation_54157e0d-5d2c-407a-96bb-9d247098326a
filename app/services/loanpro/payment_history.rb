# frozen_string_literal: true

module Loanpro
  class PaymentHistory < Service::Base
    class PaymentHistorySystemError < StandardError; end

    attribute :loan_id, presence: true
    attribute :offset
    attribute :limit

    def call
      validate!
      ordered_payments
      # TBD : Clients::LoanproApi::PaymentHistorySerializer(ordered_payments, @loan_id)
    rescue StandardError => e
      Rails.logger.error('Error in PaymentHistory', error_message: e.message)
      raise PaymentHistorySystemError, "Loanpro payment history error - #{e.message}"
    end

    private

    def ordered_payments
      ordered_payments = payment_history&.sort_by { |result| LoanproHelper.parse_date(result['date']) }&.reverse
      ordered_payments = ordered_payments.drop(@offset.to_i) if @offset.present?
      ordered_payments = ordered_payments.first(@limit.to_i) if @limit.present?

      ordered_payments
    end

    def payments
      Clients::LoanproApi.get_payments(loan_id)
    end

    def transactions
      Clients::LoanproApi.get_transactions(loan_id)
    end

    def payment_history
      index = payments&.dig('results')&.index_by { |payment| payment['id'] } || {}
      transactions['results']&.each do |transaction|
        index[transaction['paymentId']]&.merge!('transaction' => transaction)
      end
      index.values
    end
  end
end
