# frozen_string_literal: true

module ArixOnboarding
  module Webhooks
    class UpdateFundingStatus < Service::Base
      class InvalidStatusError < StandardError; end

      attribute :loan_status_params

      ARIX_LOAN_STATUS_MAP = {
        1 => ::Loan::RECEIVED,
        2 => ::Loan::DOCS_COMPLETE,
        3 => ::Loan::PASSED_COMPLIANCE,
        4 => ::Loan::APPROVED,
        5 => ::Loan::IN_FUNDING,
        6 => ::Loan::FUNDED,
        7 => ::Loan::ACCOUNTING_ENTRIES_COMPLETE,
        8 => ::Loan::READY_TO_SELL,
        9 => ::Loan::SOLD,
        10 => ::Loan::SETTLED,
        11 => ::Loan::AWAITING_FUNDING,
        80 => ::Loan::IN_REVERSE,
        90 => ::Loan::ACCOUNTING_ENTRIES_REVERSED,
        100 => ::Loan::CANCELLED,
        101 => ::Loan::COMPLIANCE_FAILED,
        102 => ::Loan::REJECTED,
        103 => ::Loan::RETURNED,
        104 => ::Loan::NOT_FULLY_FUNDED
      }.freeze

      def call
        Rails.logger.info('ArixOnboarding::WebhookCallbackHandler - Starting saving Loan status', arix_loan_id:,
                                                                                                  arix_loan_status:)

        if loan_status_params.key?('Status') && ARIX_LOAN_STATUS_MAP[arix_loan_status].blank?
          raise InvalidStatusError, "Invalid Status received from Arix: #{arix_loan_status}"
        end

        process_and_save_loan_status
        Rails.logger.info('ArixOnboarding::WebhookCallbackHandler - Finished saving Loan status', arix_loan_id:,
                                                                                                  arix_loan_status:)
      end

      private

      delegate :loan, to: :arix_funding_status

      def arix_funding_status
        @arix_funding_status ||= ArixFundingStatus.find_by!(arix_loan_id:)
      end

      def arix_loan_id
        loan_status_params['LoanId']
      end

      def arix_loan_status
        loan_status_params['Status']
      end

      def process_and_save_loan_status
        if loan_status_params.key?('Status')
          arix_funding_status.funding_status = ARIX_LOAN_STATUS_MAP[arix_loan_status]
          arix_funding_status.save!
        elsif loan_status_params.key?('FailedRulesReasons')
          save_compliance_errors!
        else
          Rails.logger.warn('Unable to process Arix webhook payload', loan_status_params:)
        end
      end

      def save_compliance_errors!
        if arix_funding_status.validation_errors.any? do |ruleset|
             ruleset.with_indifferent_access[:completed_at] == loan_status_params[:CreateDate]
           end
          Rails.logger.info('This failure reason has already been processed for this loan', arix_loan_id:)
          return
        end

        update_funding_status
      end

      def update_funding_status
        Rails.logger.warn('COMPLIANCE_FAILED', arix_loan_id:)

        arix_funding_status.funding_status = ::Loan::COMPLIANCE_FAILED
        arix_funding_status.validation_errors << validation_errors
        arix_funding_status.save!

        send_slack_notification
      end

      def validation_errors
        {
          type: 'External',
          status: 'Fail',
          description: 'Loan failed compliance ruleset',
          completed_at: loan_status_params['CreateDate'],
          rules: failed_rules_set
        }
      end

      def failed_rules_set
        # NOTE: Arix returns all rules -- successful or failed -- in the `FailedRulesReasons`
        #       property.  Only those whose "Result" is false are truly failures.
        #       We use the ActiveModel cast to defensively handle string "false" values.
        @failed_rules_set ||= loan_status_params['FailedRulesReasons']
                              .reject { |failure_reason| ActiveModel::Type::Boolean.new.cast(failure_reason['Result']) }
                              .map { |failure_reason| build_failed_rule(failure_reason) }
      end

      def build_failed_rule(failure_reason)
        {
          status: 'Fail',
          name: failure_reason['RuleName'],
          description: failure_reason['Rule'],
          data: failure_reason['Data'],
          result: failure_reason['Result'],
          run_at: loan_status_params['CreateDate']
        }
      end

      def send_slack_notification
        channel = Rails.application.config_for(:slack_channels).crb_onboarding_failures_channel
        AmsSlackBot.post_message_blocks(message_blocks:, channel:)

        Rails.logger.info("Slack notification sent to channel: #{channel}")
      end

      def message_blocks
        failed_rules_names = failed_rules_set.map { |rule| rule[:name] }.join(', ')

        [{
          type: 'section', text: { type: 'mrkdwn', text: '*:x: Arix Compliance Failure*' }
        },
         {
           type: 'section',
           text: { type: 'mrkdwn',
                   text: "*UID:* `#{loan.unified_id}` \n" \
                         "*Failed Rules:* `#{failed_rules_names}` \n" \
                         "*Contract Date:* `#{contract_date}` \n" }
         },
         {
           type: 'actions',
           elements: [{
             type: 'button',
             text: { type: 'plain_text', text: 'View Compliance Failures in Dash' },
             url: "#{dash_base_url}/admin/ams_funding/#{arix_funding_status.id}/arix_submission_details",
             style: 'primary'
           }]
         }]
      end

      def contract_date
        til_history = loan.til_histories.where.not(signed_at: nil).take
        til_history.til_data.dig('loan', 'contractDate')
      rescue StandardError
        'Unable to retrieve contract date'
      end

      def dash_base_url
        Rails.application.config_for(:general).dash_base_url
      end
    end
  end
end
