# frozen_string_literal: true

module ArixOnboarding
  module Attributes
    class CustomField
      attr_reader :decision_engine_input, :decision_engine_output, :installment_loan_agreement, :lead, :loan,
                  :loan_detail, :socure

      NSFS_3_MONTHS = 0.0

      def self.attr(...)
        new(...).attr
      end

      def initialize(loan, loan_detail, lead, installment_loan_agreement)
        @loan = loan
        @loan_detail = loan_detail
        @lead = lead
        @installment_loan_agreement = installment_loan_agreement
        @socure = FundingDocuments::Socure.new(loan.id)
        @decision_engine_input = FundingDocuments::DecisionEngineInput.new(loan.id)
        @decision_engine_output = FundingDocuments::DecisionEngineOutput.new(loan.id)
      end

      def attr
        fields = {
          Field1: ila_document&.ip_address,
          Field2: socure.global_watchlist,
          Field3: field3,
          Field4: installment_loan_agreement.name,
          Field5: custom_field5_attributes.to_json,
          Field7: custom_field7_attributes.to_json
        }
        fields[:Field6] = custom_field6_attributes.to_json if loan.ipl?
        fields
      end

      private

      def custom_field5_attributes
        loan.ipl? ? custom_field5_attributes_ipl : custom_field5_attributes_upl
      end

      def custom_field5_attributes_ipl # rubocop:disable Metrics/AbcSize
        {
          MonthlyHousingExpense: loan.monthly_housing_payment,
          CreditPolicyVersion: decision_engine_output.version_policy,
          TotalAmountEnrolledDebt: loan_detail.total_amount_enrolled_debt,
          DebtResolutionProgramEnrollmentDate: loan_detail.beyond_enrollment_date&.to_date&.iso8601,
          DebtResolutionProgramEnrollmentStatus: 'Enrolled',
          DebtResolutionProgramName: lead.service_entity_name,
          CashBackOffered: loan.selected_offer.cashout_amount&.positive?,
          CashBackAmount: loan.selected_offer.cashout_amount,
          NumCreditCardsOpened730DaysPreBeyond: decision_engine_input.credit_cards_opened_730_days_pre_beyond_count,
          NumTradesOpened730DaysPilAdjusted: decision_engine_input.trades_opened_within_730_days_pil_adjusted_count,
          NumSettledTradesPaidChargeOffsPaidCollections:
            decision_engine_input.settled_trades_paid_charge_offs_paid_collecions_pre_beyond_count,
          PreviouslyDeclinedForAboveLoan: previously_declined_for_non_passable_reason
        }
          .merge!(credit_model_field5_attributes_ipl)
      end

      # These attributes are specific to Credit Model v1.0. They can be
      # merged into the main Field5 logic once 100% of loans have run
      # through the model for at least 28 days.
      def credit_model_field5_attributes_ipl # rubocop:disable Metrics/AbcSize
        return {} unless Flipper.enabled?(:credit_model_arix_fields)
        return {} if experiment_cohort == 'champion'

        output = decision_engine_output
        {
          CreditModelVersion: output.credit_model_version,
          EmployedFullTimeCapped: output.employed_full_time_capped,
          FicoBeacon5Capped: output.fico_beacon5_capped,
          LoanToIncomeCapped: output.loan_to_income_capped,
          ModelLogisticScore: output.model_logistic_score,
          NumBankruptcyOrChapter7EverCapped: output.number_bankruptcy_or_chapter_7_ever_capped,
          NumConsumerDisputeIndicatorCapped: output.num_consumer_dispute_indicator_capped,
          NumInquiries120DaysCapped: output.num_inquiries_within_120_days_capped,
          NumOpenMortgageCapped: output.num_open_mortgage_capped,
          NumTradesOpened730DaysPilAdjustedCapped: output.trades_opened_730_days_pil_adjusted_capped,
          NumberOfReturnedDepositsInLast180DaysCapped: output.number_of_returned_deposits_in_last_180_days_capped,
          PaymentShockCapped: output.payment_shock_capped,
          PctOpenedOfEverPast12MosPreBeyondCapped: output.pct_opened_of_ever_past_12mos_pre_beyond_capped,
          ProgramFirstApplicationCapped: output.program_first_application_capped
        }
      end

      def custom_field5_attributes_upl
        {
          CreditPolicyVersion: decision_engine_output.version_policy,
          Financial_Institution_Check: approved_todo_exists?('bank'),
          Num_60DaysPastDue: decision_engine_input.past_due_60_days_count,
          Num__ChargeOffs_Within6Months: decision_engine_input.charge_offs_within_6_months_count,
          Num_Bankruptcy: decision_engine_input.bankruptcy_count,
          Num_ChargeOffs_Within36Months: decision_engine_input.charge_offs_within_36_months_count,
          Num_DerogatoryTrades: decision_engine_input.derogatory_trade_count,
          Num_RecentInquiries_LessThan3Months: decision_engine_input.recent_inquiries_within_3_months_count,
          Num_RecentInquiries_LessThan6Months: decision_engine_input.recent_inquiries_within_6_months_count,
          Sum_RevolvingCreditLimitAmount: decision_engine_input.revolving_credit_limit,
          Sum_RevolvingUnpaidBalanceAmount: decision_engine_input.revolving_unpaid_balance
        }
      end

      def custom_field6_attributes
        {
          DscNsfs3Months: NSFS_3_MONTHS,
          DscPaymentAdheranceRatio3Months: payment_adherence_ratio_3_months,
          DscPaymentAdheranceRatio6Months: payment_adherence_ratio_6_months,
          DscPaymentAmount: loan.monthly_deposit_amount,
          DscAccountBalance: loan_detail.estimated_cft_deposits
        }
      end

      def custom_field7_attributes
        {
          NumRecentBankruptcyWithin7Years: decision_engine_input.recent_bankruptcy_within_7_years_count,
          NumPetitionedBankruptcy: decision_engine_input.petitioned_bankruptcy_count,
          NumRecentAutoDelinquency3Months: decision_engine_input.recent_auto_delinquency_3_months_count,
          NumRecentAutoDelinquency6Months: decision_engine_input.recent_auto_delinquency_6_months_count,
          NumRecentMortgageDelinquency3Months: decision_engine_input.recent_mortgage_delinquency_3_months_count,
          NumRecentMortgageDelinquency6Months: decision_engine_input.recent_mortgage_delinquency_6_months_count
        }
      end

      def previously_declined_for_non_passable_reason
        decision_engine_input.ipl_previously_declined? &&
          !decision_engine_input.previously_declined_for_only_passable_reasons?
      end

      def ila_document
        @ila_document ||= begin
          ila_type = loan.ipl? ? :CRB_INSTALLMENT_LOAN_AGREEMENT : :DM_CRB_INSTALLMENT_LOAN_AGREEMENT
          loan.docs.joins(:template).find_by(template: { type: DocTemplate::TYPES[ila_type] })
        end
      end

      def payment_adherence_ratio_3_months
        loan.loan_detail.payment_adherence_ratio_3_months.try(:*, 100)
      end

      def payment_adherence_ratio_6_months
        loan.loan_detail.payment_adherence_ratio_6_months.try(:*, 100)
      end

      def field3
        result = { 'kyc' => socure.kyc.to_s }

        if approved_todo_exists?('fraud_alert', check_docs: false)
          result.merge!('CIP Verification used' => { 'fraud' => 'BorrowerIdentityConfirmed' })
        end

        result.to_json
      end

      def approved_todo_exists?(type, check_docs: true)
        query = Todo.approved.where(loan_id: loan.id, type:)
        query = query.joins(:todo_docs).merge(TodoDoc.where.not(status: 'rejected')) if check_docs
        query.exists?
      end

      def experiment_cohort
        @experiment_cohort ||= Experiment['2025_04_CHI_1753_Credit_Model_1_0'].fetch_cohort_for(loan.borrower)
      end
    end
  end
end
