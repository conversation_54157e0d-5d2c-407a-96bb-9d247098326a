# frozen_string_literal: true

require 'new_relic/agent/method_tracer'

module Ams
  module Api
    module Loans
      class UplApplyForLoan < ServiceObject
        class MultipleOffersError < BadRequest; end
        class GdsMissingSsnError < BadRequest; end

        LOAN_PURPOSES = %w[debt_consolidation credit_card_refinancing large_purchases medical_dental home_improvement
                           moving_relocation other].freeze

        attribute :amount, :float
        attribute :loan_purpose, :string
        attribute :first_name, :string
        attribute :last_name, :string
        attribute :address_street, :string
        attribute :city, :string
        attribute :state_code, :string
        attribute :zip_code, :string
        attribute :phone_number, :string
        attribute :email, :string
        attribute :date_of_birth, :string
        attribute :income, :float
        attribute :monthly_housing_payment, :float
        attribute :ssn, :string
        attribute :oppId, :string

        validates :amount, :loan_purpose, :first_name, :last_name, :address_street, :city, :state_code, :zip_code,
                  :phone_number, :email, :date_of_birth, :income, :monthly_housing_payment, presence: true

        validates :loan_purpose, inclusion: { in: LOAN_PURPOSES }
        validates :state_code, length: { is: 2 }
        validates_format_of :zip_code, with: RegexHelper::ZIP_CODE_REGEX,
                                       message: 'is in the wrong format. It should be in the form 12345 or 12345-1234'
        validates :phone_number, length: { is: 10 }
        validates :email, length: { maximum: 254 }, format: { with: RegexHelper::EMAIL_REGEX }
        validates :date_of_birth, format: { with: RegexHelper::DATE_OF_BIRTH_REGEX }
        validates :ssn, format: { with: RegexHelper::SSN_REGEX }, allow_nil: true

        def call
          call_service_object do
            send_disclosures_email
            gds_results = run_upl_credit_policy
            offer = extract_offer(gds_results)
            loan_inquiry = create_loan_inquiry(gds_results, offer)
            process_result(loan_inquiry)
            build_response(loan_inquiry)
          end
        end

        private

        def run_upl_credit_policy
          Clients::GdsApi.upl_apply(application: application_details)
        end

        def extract_offer(gds_results)
          offers = gds_results['offers'] || []
          if offers.length > 1
            raise MultipleOffersError, "UPL loan with request_id #{gds_results['request_id']} got more than one " \
                                       'offer. This is currently unsupported.'
          end

          return nil if offers.empty?

          raise GdsMissingSsnError, 'SSN missing in GDS UPL apply response' if gds_results['ssn'].blank?

          format_offer(offers.first)
        end

        def format_offer(gds_offer)
          formatted_offer = {
            amount_financed: gds_offer['amount_financed'],
            amount: gds_offer['amount'],
            apr: gds_offer['apr'],
            interest_rate: gds_offer['interest_rate'],
            offer_creation_date: gds_offer['offer_creation_date'],
            offer_id: gds_offer['offer_id'],
            origination_fee_amount: gds_offer['origination_fee_amount'],
            origination_fee_percent: gds_offer['origination_fee_percent'],
            payment_amount: gds_offer['payment'],
            term: gds_offer['term'],
            payment_frequency: 'monthly',
            # AMS only supports CRB loans (https://abovelending.slack.com/archives/C060RHN21UH/p1700263204615839?thread_ts=1700262999.380379&cid=C060RHN21UH)
            originating_party: ::Loan::ORIGINATING_PARTIES[:CRB]
          }

          if gds_offer['offer_creation_date'].present?
            formatted_offer['expiration_date'] = offer_expiration_date(gds_offer)
          end

          formatted_offer
        end

        def offer_expiration_date(gds_offer)
          gds_offer['offer_creation_date'].to_date + Rails.application.config_for(:general).offer_expiration_days!.days
        rescue Date::Error => e
          Rails.logger.error("#{self.class.name} - Failed to parse creation date in GDS offer. Value: " \
                             "#{gds_offer['offer_creation_date']}; Error: #{e.class} - #{e.message}")
          nil
        end

        def create_loan_inquiry(gds_results, offer)
          ::LoanInquiry.create!(
            id: SecureRandom.uuid,
            gds_request_id: gds_results['request_id'],
            beyond_request_tracking_id: SecureRandom.uuid,
            application: application_details.merge(ssn: gds_results['ssn']),
            offers: offer.present? ? [offer] : nil,
            decline: gds_results['rejection_data']
          )
        rescue StandardError => e
          logger.error('Error creating Loan Inquiry', class: self.class, exception: e,
                                                      gds_request_id: gds_results['request_id'])
          raise UnprocessableEntity, e
        end

        def process_result(loan_inquiry)
          return handle_declined_inquiry(loan_inquiry) if loan_inquiry.offers.blank?

          send_onboarding_email(loan_inquiry)
          Upl::SendOfferEmail.call(loan_inquiry:)
        end

        def handle_declined_inquiry(loan_inquiry)
          Upl::LoanDataRecordsCreator.call(
            loan_inquiry:,
            loan_app_status: ::LoanAppStatus::FRONT_END_DECLINED_STATUS,
            create_loan_with_unified_id: false,
            verified_borrower: false
          )
          Upl::DeliverNoticeOfAdverseActionJob.perform_async(loan_inquiry.id)
        end

        def send_onboarding_email(loan_inquiry)
          full_name = "#{first_name} #{last_name}"
          lander_base_url = Rails.application.config_for(:general).lander_base_url
          welcome_link = "#{lander_base_url}/create-account/#{loan_inquiry.id}"

          Clients::CommunicationsServiceApi.send_message!(
            recipient: email, template_key: Clients::CommunicationsServiceApi::UPL_ONBOARDING_TEMPLATE,
            inputs: { full_name:, link: welcome_link }
          )
        end

        def build_response(loan_inquiry)
          @body = { request_tracking_id: loan_inquiry.beyond_request_tracking_id }
          @body[:oppId] = oppId if oppId.present?

          if loan_inquiry.offers.blank?
            @status = 200
            @body.merge!(
              status: ::LoanAppStatus::FRONT_END_DECLINED_STATUS,
              reason: loan_inquiry.decline['decline_reason_text'],
              reasons: loan_inquiry.decline['decline_reasons']
            )
          else
            @status = 201
            @body.merge!(
              status: ::LoanAppStatus::OFFERED_STATUS,
              offer: loan_inquiry.offers.first
            )
          end
        end

        def application_details
          @application_details ||= build_application_details
        end

        def build_application_details # rubocop:disable Metrics/AbcSize
          {
            amount:, loan_purpose:, first_name:, last_name:, address_street:, city:, state_code:,
            phone_number:, email:, date_of_birth:, income:, monthly_housing_payment:, ssn:
          }.tap do |app_details|
            # The `oppId` key must be completely absent when not specified in the request params
            app_details[:oppId] = oppId unless oppId.blank?
            app_details[:zip_code] = zip_code[0..4] # removes extra identifier of zip code if present
          end
        end

        def send_disclosures_email
          template_key = Clients::CommunicationsServiceApi::INFO_AND_DISCLOSURE_TEMPLATE
          Clients::CommunicationsServiceApi.send_message!(recipient: email, template_key:, inputs: { first_name: })
        end
      end
    end
  end
end
