# frozen_string_literal: true

module Ams
  module Api
    module Offers
      class Generated < ServiceObject # rubocop:disable Metrics/ClassLength
        include Notifier

        class MissingCreditFileError < BadRequest; end

        EXPIRATION_DAYS = Rails.application.config_for(:general).offer_expiration_days!.days
        MISSING_CREDIT_FILE_DECLINE_REASON = 'Credit bureau file not returned'
        CREDIT_MODEL_EXPERIMENT = '2025_04_CHI_1753_Credit_Model_1_0'

        attribute :request_id, :string
        attribute :app_status, :string
        attribute :decision_reason_number, :string
        attribute :decline_reason_text, :string
        attribute :credit_score, :decimal
        attribute :score_factor, :string
        attribute :originating_party, :string
        attribute :decline_reasons, array: true
        attribute :offers, array: true
        attribute :credit_freeze, :boolean, default: false
        attribute :credit_model_level, :string
        attribute :credit_model_score, :decimal
        attribute :reassigned_cohort, :string, default: nil

        validates :request_id, presence: true
        validates :app_status, inclusion: {
          in: ::LoanAppStatus::ID_TO_NAME,
          message: "must be one of [#{::LoanAppStatus::ID_TO_NAME.join(', ')}]",
          context: { valids: ::LoanAppStatus::ID_TO_NAME }
        }
        validates :originating_party, inclusion: {
          in: ::Loan::ORIGINATING_PARTIES.values,
          message: "should be one of #{::Loan::ORIGINATING_PARTIES.values}"
        }
        validates :reassigned_cohort, inclusion: { in: %w[challenger champion] }, allow_nil: true
        # TODO: validate offers after ASUN-538

        def call
          call_service_object do
            @body = { offers: [] }

            validate_loan
            handle_credit_model
            handle_credit_freeze
            update_reasigned_cohort
            validate_credit_file_presence

            @status = 201

            if loan.loan_app_status_id == ::LoanAppStatus.id(LoanAppStatus::FRONT_END_DECLINED_STATUS)
              log_info("Ignore since Loan with request_id #{request_id} is already in FRONT_END_DECLINED state")
              next
            end

            parsed_offers.empty? ? handle_decline : process_offers
          rescue ::Offers::HandleCreditFreeze::ActiveCreditFreezeError => e
            # GDS does not handle credit freeze errors, so we only log it and return a success response.
            @status = 200
            log_exception(e, ignore_notice_error: true)
          end
        end

        private

        def validate_loan
          raise BadRequest, 'No loan with given request_id' unless loan
        end

        def handle_credit_model
          loan.loan_detail.update(credit_model_level:, credit_model_score:)
        end

        def handle_credit_freeze
          ::Offers::HandleCreditFreeze.call(loan:, credit_freeze_active: credit_freeze)
        end

        def update_reasigned_cohort
          return if Flipper.enabled?(CREDIT_MODEL_EXPERIMENT)
          return unless reassigned_cohort.present?

          Experiment[CREDIT_MODEL_EXPERIMENT].force_cohort!(loan.borrower, cohort: reassigned_cohort)
        end

        def validate_credit_file_presence
          return unless credit_score.to_i.zero? && decline_reason_text == MISSING_CREDIT_FILE_DECLINE_REASON

          raise MissingCreditFileError, 'Missing credit file, offers need to be regenerated by CaseCenter.'
        end

        def handle_decline
          update_loan_as_front_end_decline
          ::Loans::DeliverNoticeOfAdverseActionJob.perform_async(loan.id)

          @body = { offers: [] }
        end

        def update_loan_as_front_end_decline
          log_info("Loan #{request_id} has been declined")
          loan.update!(
            should_send_adverse_action: true,
            loan_app_status_id: ::LoanAppStatus.id(LoanAppStatus::FRONT_END_DECLINED_STATUS),
            decision_reason_number:, decline_reason_text:,
            credit_score:, score_factor:,
            originating_party:, decline_reasons:
          )
        end

        def send_onboarding_mail
          # NOTE:  Onboarding email is sent earlier for web-originated accounts.
          return if loan.source_type == LoanApplications::Pi1::SOURCE_TYPE

          Users::SendWelcomeEmail.call(email: loan.borrower.email)
        end

        def process_offers
          log_info("Loan #{request_id} has received new offers")
          saved_offers = loan.offers.create!(parsed_offers)
          loan.update!(
            loan_app_status_id: ::LoanAppStatus.id(app_status),
            originating_party: parsed_offers.first[:originating_party]
          )

          send_onboarding_mail

          trigger_setup_above_contact_request if loan.source_type == LoanApplications::Pi1::SOURCE_TYPE

          loanpro_offers = fetch_apr_calculations(saved_offers)

          @body = { offers: loanpro_offers }
        end

        def fetch_apr_calculations(saved_offers)
          Rails.logger.measure_info('Fetching all APR Calculations for Offers Generated',
                                    metric: 'Gds/calculate_apr') do
                                      fetch_apr_calculations_from_api_calculator(saved_offers)
                                    end
        rescue StandardError => e
          Rails.logger.error('Error fetching APR:')
          log_exception(e, ignore_notice_error: true)
          []
        end

        def trigger_setup_above_contact_request
          SendSetupAboveContactRequestJob.perform_async(loan.id)
        end

        def fetch_apr_calculations_from_api_calculator(saved_offers)
          Loanpro::AprCalculator.call(loan:, offers: saved_offers, used: true).map do |loanpro_offer_data|
            loanpro_offer_hash(loanpro_offer_data)
          end
        end

        def loanpro_offer_hash(loanpro_offer_data)
          {
            external_offer_id: loanpro_offer_data.external_offer_id,
            apr: loanpro_offer_data.apr,
            term: loanpro_offer_data.term,
            principal_loan_amount: loanpro_offer_data.principal_loan_amount
          }
        end

        def parsed_offers # rubocop:disable Metrics/AbcSize, Metrics/MethodLength
          return [] unless offers.is_a?(Array)

          @parsed_offers ||= offers.map do |o|
            next nil unless o['offer_id']

            o = o.deep_symbolize_keys

            {
              id: SecureRandom.uuid,
              external_offer_id: o[:offer_id],
              uri: o[:offer_url],
              is_hero: o[:hero_offer],
              external_creation_date: o[:offer_creation_date],
              amount: o[:principal_loan_amount],
              origination_fee: o[:origination_fee_amount],
              origination_fee_percent: o[:origination_fee_percent].to_d * 100, # GDS sends the percent as a factor
              originating_party: o[:originating_party] || ::Offer::ORIGINATING_PARTIES[:DIRECT_LICENSES],
              cashout_amount: o[:cash_out_amount],
              expiration_date: DateTime.now + EXPIRATION_DAYS,
              lender: 'Above Lending',
              lender_logo_uri: 'http://abovelending.com',
              type: 'regular',
              shown_to_customer: false,
              **o.slice(*%i[advanced_period_interest_per_term final_term_payment lender_network amount_financed term
                            monthly_payment interest_rate total_advance_period_interest initial_term_payment
                            settlement_amount description term_frequency])

            }
          end.compact

          @parsed_offers
        end

        def loan
          ::Loan.includes(:loan_detail).find_by(request_id:)
        end
      end
    end
  end
end
