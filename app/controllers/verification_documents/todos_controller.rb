# frozen_string_literal: true

module VerificationDocuments
  class TodosController < BaseController
    before_action :enqueue_gds_task_sync, only: :index
    before_action :ensure_todo_authorized, only: %i[document_create show]
    skip_before_action :ensure_correct_funnel_step!, only: %i[list show]

    helper_method :bank_account?, :all_todos, :all_statuses

    def index; end

    def list; end

    def show; end

    def document_create
      doc_params = params.permit(:id, files: {}).to_h

      svc = ::Ams::Api::Todos::CreateDocument.new(todoId: doc_params[:id], files: doc_params[:files])
      svc.custom_authorization = true
      svc.call

      head svc.status
    end

    private

    def bank_account?
      return @bank_account if defined?(@bank_account)

      @bank_account = current_loan.borrower.bank_account&.enabled
    end

    def all_todos
      return @all_todos if defined?(@all_todos)

      @all_todos = ::Todo.latest_unique.where(loan: current_loan, type: ::Todo::ALLOWED_TYPES)
    end

    def all_statuses
      result = { bank_account: bank_account? }

      all_todos.each do |todo|
        result.merge!(todo.id => todo.all_set?)
      end

      result
    end

    def enqueue_gds_task_sync
      Gds::TriggerTodoResyncJob.perform_async(current_loan.request_id)
    end

    def ensure_todo_authorized
      # Ensure that this todo matches the loan in session, otherwise throw a 404
      #
      @todo = ::Todo.find_by!(id: params[:id], loan: current_loan)
    end
  end
end
