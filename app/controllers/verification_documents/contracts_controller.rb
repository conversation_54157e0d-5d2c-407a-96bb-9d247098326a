# frozen_string_literal: true

module VerificationDocuments
  class ContractsController < BaseController
    skip_before_action :ensure_authenticated_borrower!, only: %i[contract_auth sign_completed]
    skip_before_action :ensure_correct_funnel_step!, only: %i[contract contract_auth sign_completed]
    # TODO: Remove this once `around_action :record_request` has been removed from LoanApplications::BaseController (https://abovelending.atlassian.net/browse/CHI-1351)
    # The request event is recorded explicitly in each controller action.
    skip_around_action :record_request, only: %i[contract]

    def index; end

    def contract
      @contract_generating = contract_generating?

      return regenerate_contract if @contract_generating
      raise StandardError, 'Invalid APR' unless valid_apr?

      @contract_signing_url = contract_signing_url
      @til_history = til_history
    rescue StandardError
      render status: :bad_request
    ensure
      LoanApplications::RecordRequestEvent.call(event_agent: :user, request_event_name:, request:, response:,
                                                resolver:, meta: contract_meta)
    end

    def contract_auth
      loan = ::Loan.find_by!(contract_signing_token: params[:token],
                             loan_app_status_id: LoanAppStatus.id(LoanAppStatus::APPROVED_STATUS))

      sign_in(loan.borrower)

      redirect_to contracts_path
    rescue ActiveRecord::RecordNotFound
      redirect_to signin_borrowers_path(redirect: contracts_path)
    end

    def sign_completed
      @event = consolidated_event_for(params[:event])
      @heap_state = heap_state_for(@event)
      render layout: 'blank'
    end

    def congratulations; end

    protected

    def contract_meta
      default_meta.merge(
        has_loanpro_loan: loanpro_loan.present?,
        is_contract_generating: contract_generating?,
        is_valid_apr: valid_apr?,
        has_til_history: til_history.present?,
        has_docusign_envelope_id: til_history&.docusign_envelope_id.present?,
        has_contract_signing_url: @contract_signing_url.present?
      )
    end

    private

    def valid_apr?
      return false unless loanpro_loan&.loanpro_raw_response

      loanpro_loan.valid_apr?
    end

    def contract_generating?
      loanpro_loan&.contract_generated_at.blank?
    end

    # Generating contracts can cause race conditions, we're relying on Sidekiq Unique jobs
    # to ensure this is run only once.
    def regenerate_contract
      ::Contracts::GenerateContractJob.perform_async(current_loan.id, current_borrower.email)
    end

    def loanpro_loan
      return @loanpro_loan if defined? @loanpro_loan

      @loanpro_loan = current_loan.loanpro_loan
    end

    def til_history
      return @til_history if defined? @til_history

      @til_history = loanpro_loan&.til_history
    end

    def contract_signing_url
      return @contract_signing_url if defined? @contract_signing_url

      docusign_envelope_id = til_history&.docusign_envelope_id
      @contract_signing_url = ::Contracts::CreateDocusignRecipientView.call(loan: current_loan, docusign_envelope_id:)
    end

    def consolidated_event_for(docusign_event)
      case docusign_event
      when 'signing_complete'
        'DOCUSIGN_DOCUMENTS_SIGNING_SUCCESSFUL'
      when 'cancel', 'decline', 'session_timeout', 'ttl_expired'
        'DOCUSIGN_DOCUMENTS_SIGNING_TIMED_OUT_OR_DECLINED'
      else
        'DOCUSIGN_DOCUMENTS_SIGNING_FAILED'
      end
    end

    def heap_state_for(consolidated_event)
      case consolidated_event # rubocop:disable Style/HashLikeCase
      when 'DOCUSIGN_DOCUMENTS_SIGNING_SUCCESSFUL'
        'signing success'
      when 'DOCUSIGN_DOCUMENTS_SIGNING_TIMED_OUT_OR_DECLINED'
        'contract declined failure'
      when 'DOCUSIGN_DOCUMENTS_SIGNING_FAILED'
        'signing failure'
      end
    end
  end
end
