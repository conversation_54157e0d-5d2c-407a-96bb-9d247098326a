# frozen_string_literal: true

module LoanApplications
  class Reapply<PERSON><PERSON>roller < BaseController
    skip_before_action :ensure_authenticated_borrower!
    before_action :ensure_reapply_authenticated!, except: %i[reapply reapply_create]

    before_action :initialize_reapply_form_model, only: %i[reapply reapply_create]
    before_action :initialize_view_form_model, only: %i[continue continue_create]
    before_action :initialize_edit_form_model, only: %i[continue_edit continue_save]

    def reapply; end

    def reapply_create
      return unless @form_model.valid?

      borrower = find_borrower
      sign_in(borrower)

      session[:reapply_data] = LoanApplications::DataForResubmission.call(borrower:)

      redirect_to continue_loan_applications_path
    rescue ActiveRecord::RecordNotFound
      @message_level = :error
      @message = "The last 4 digits entered don't match what we have on record. Please verify and enter the correct last
       4 digits of your Social Security number. If you continue to have issues, please call us at
       #{helpers.service_entity_phone_number(session[:service_entity],
                                             class: 'text-right md-text-left text-brand-blue-500 py-1 underline')}."
    rescue StandardError => e
      log_exception(e)

      @message_level = :error
      @message = 'An error has occurred. Please refresh the page and try again.'
    ensure
      render :reapply, status: :unprocessable_entity unless performed?
    end

    def continue_edit; end

    def continue_save
      return unless @form_model.valid?

      @reapply_data[:date_of_birth] = @form_model.date_of_birth
      @reapply_data.merge!(@form_model.reapplication_attributes)

      session[:reapply_data] = @reapply_data

      redirect_to continue_loan_applications_path
    ensure
      render :continue_edit, status: :unprocessable_entity unless performed?
    end

    def continue; end

    def continue_create
      return unless @form_model.valid?

      LoanApplications::Resubmit.call(submission_attributes)

      sign_in(current_borrower)

      redirect_to select_offer_loan_applications_path
    ensure
      render :continue, status: :unprocessable_entity unless performed?
    end

    private

    def find_borrower
      Borrower.find_by_magic_link!(token: params[:token], last_four_ssn: @form_model.last_four_ssn)
    end

    def ensure_reapply_authenticated!
      return redirect_to thank_you_exit_pages_path if session[:reapply_data].blank?

      @reapply_data = session[:reapply_data].symbolize_keys
    end

    def initialize_reapply_form_model
      @form_model = ReapplyFormModel.new(
        **params.require(:loan_applications_reapply_form_model)
                .permit(*ReapplyFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      @form_model = ReapplyFormModel.new
    end

    def initialize_view_form_model
      @form_model = ReapplyViewFormModel.new(
        **params.require(:loan_applications_reapply_view_form_model)
                .permit(*ReapplyViewFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      @form_model = ReapplyViewFormModel.new
    end

    def initialize_edit_form_model
      @form_model = ReapplyEditFormModel.new(
        **params.require(:loan_applications_reapply_edit_form_model)
                .permit(*ReapplyEditFormModel.attribute_names)
      )
    rescue ActionController::ParameterMissing
      attribute_names = ReapplyEditFormModel.attribute_names.push(:date_of_birth).map(&:to_sym)
      attributes = @reapply_data.slice(*attribute_names)
      @form_model = ReapplyEditFormModel.new(attributes)
    end

    def submission_attributes
      attributes = @reapply_data.merge(@form_model.reapplication_attributes)

      attributes.compact_blank!
      attributes.slice!(*LoanApplications::Resubmit.attribute_names.map(&:to_sym))
      attributes.merge!(
        client_ip: forwarded_for_ip_address(default_to_remote_ip: true),
        borrower_id: current_borrower&.id,
        code: current_loan&.code
      )

      attributes
    end
  end
end
