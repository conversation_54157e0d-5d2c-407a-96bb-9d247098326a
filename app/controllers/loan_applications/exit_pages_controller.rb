# frozen_string_literal: true

module LoanApplications
  class ExitPagesController < BaseController
    skip_before_action :ensure_authenticated_borrower!
    skip_before_action :ensure_correct_funnel_step!

    def active_application; end

    def credit_freeze; end

    def application_processing; end

    def no_offer; end

    def thank_you
      @footer_contact_info = true
      @footer_credibility = false
    end

    def whoops; end
  end
end
