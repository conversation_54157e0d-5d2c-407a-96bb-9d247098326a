# frozen_string_literal: true

module LoanApplications
  class <PERSON><PERSON>reezeController < BaseController
    include MaintenanceModeEnforceable

    # TODO: Remove this once `around_action :record_request` has been removed from LoanApplications::BaseController (https://abovelending.atlassian.net/browse/CHI-1351)
    # The request event is recorded explicitly in each controller action.
    skip_around_action :record_request

    def credit_freeze_resubmit
      credit_freeze_resubmit_service.call

      reset_offer_wait_session_data
      redirect_to select_offer_loan_applications_path(offer: session[:code], s: session[:service_entity])
    rescue CreditFreezeResubmit::CreditFreezeNotActiveError => e
      @credit_freeze_active = false
      handle_credit_freeze_error('Credit freeze resubmission failure.', e)
    ensure
      ::RecordRequestEvent.call(request_event_name:, request:, response:, meta: credit_freeze_resubmit_meta)
    end

    protected

    def credit_freeze_resubmit_meta
      @credit_freeze_active = true if @credit_freeze_active.nil?

      default_meta.merge(
        is_in_maintenance_mode: in_maintenance_mode?,
        credit_freeze_active: @credit_freeze_active
      ).merge(credit_freeze_resubmit_service.meta)
    end

    private

    def credit_freeze_resubmit_service
      @credit_freeze_resubmit_service ||= LoanApplications::CreditFreezeResubmit.new(loan: current_loan)
    end

    def reset_offer_wait_session_data
      # Clear the offer poll start timestamp to prevent routing user to application processing page after a delayed resubmission
      session.delete(:offer_wait_at)
    end

    def handle_credit_freeze_error(message, error)
      Rails.logger.error(message:, errors: [error.message], loan_id: current_loan&.id)

      redirect_to_whoops_path(message: error.message)
    end
  end
end
