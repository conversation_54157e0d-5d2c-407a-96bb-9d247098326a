# frozen_string_literal: true

module Api
  class OffersController < ApiController
    before_action :set_event_agent_user, only: [:show]
    before_action :set_event_agent_gds, only: %i[generated save_selection]
    around_action :record_request_event

    before_action do
      Current.allowed_apps = [ExternalApp::INTERNAL_APP, ExternalApp::EXTERNAL_APP]
    end

    def generated
      handle_ams_service_action
    end

    def save_selection
      handle_ams_service_action
    end

    # Used with Integration tests
    def show
      handle_ams_service_action
    end

    private

    def generated_params # rubocop:disable Metrics/MethodLength
      params.permit(
        :request_id,
        :app_status,
        :decision_reason_number,
        :decline_reason_text,
        :credit_score,
        :score_factor,
        :originating_party,
        :credit_freeze,
        :credit_model_level,
        :credit_model_score,
        :reassigned_cohort,
        decline_reasons: [],
        offers: %i[
          offer_id offer_url hero_offer offer_creation_date
          lender_network principal_loan_amount amount_financed term
          monthly_payment interest_rate origination_fee_amount
          origination_fee_percent total_advance_period_interest
          advanced_period_interest_per_term initial_term_payment
          final_term_payment originating_party settlement_amount
          cash_out_amount description term_frequency
        ]
      )
    end

    def save_selection_params
      params.permit(:request_id, :offer_id, :app_status).to_h
    end

    def show_params
      { loan_id: params[:loanId] }
    end
  end
end
