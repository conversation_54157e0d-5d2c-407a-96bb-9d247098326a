require:
  - ./lib/rubocop/documentation/do_not_stub_flipper_cop.rb

AllCops:
  SuggestExtensions: false
  NewCops: enable

  Exclude:
    - "bin/**/*"
    - "vendor/**/*"
    - "db/**/*"
    - "node_modules/**/*"
    - "Guardfile"
    - "bundle/**/*"

Layout/LineLength:
  Exclude:
    - "spec/**/*"
    - "db/schema.rb"
    - "db/abovelending_schema.rb"
    - "db/abovelending_shadow_schema.rb"
  AllowedPatterns:
    - !ruby/regexp /^ *# /

Metrics/BlockLength:
  Exclude:
    - "config/routes.rb"
    - "config/environments/development.rb"
    - "config/initializers/sidekiq.rb"
    - "spec/**/*"
    - "lib/tasks/**/*"

Naming/VariableNumber:
  AllowedPatterns:
    - '^experiment_.*$'
    - 'credit_test_1'

Metrics/ClassLength:
  Max: 150

Metrics/MethodLength:
  Max: 20

Style/Documentation:
  Enabled: false

Style/KeywordArgumentsMerging:
  Enabled: false

Style/RegexpLiteral:
  EnforcedStyle: mixed

Style/SafeNavigationChainLength:
  Enabled: false

Metrics/ParameterLists:
  Exclude:
    - "app/components/ui/**/*"

Metrics/AbcSize:
  # Treats repeated calls on `model` as a single branch, facilitating
  # compliance with the default max ABC size of 17 for logging purposes,
  # without necessitating an increase in the threshold.
  #
  # For reference https://msp-greg.github.io/rubocop/file.cops_metrics.html
  CountRepeatedAttributes: false

  # Only allow system-specific methods listed here. Avoid unnecessary overrides.
  AllowedMethods:
    - meta

Metrics/CyclomaticComplexity:
  # Only allow system-specific methods listed here. Avoid unnecessary overrides.
  AllowedMethods:
    - meta

Metrics/PerceivedComplexity:
  # Only allow system-specific methods listed here. Avoid unnecessary overrides.
  AllowedMethods:
    - meta
