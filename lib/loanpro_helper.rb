# frozen_string_literal: true

# LoanPro's API is a bit cludgy. These helpers will make it easier to work with.
class LoanproHelper
  # LoanPro dates are serialized by default in an outdated Microsoft format (e.g. "/Date(1758326400)/"). This class
  # provides various utility methods to assist in parsing and working with dates in this format.
  def self.parse_date(ms_string_value)
    return nil if ms_string_value.blank?

    ms_string_value = ms_string_value.to_s
    return nil unless ms_string_value.start_with?('/Date(') && ms_string_value.end_with?(')/')

    Time.zone.at(ms_string_value[6..-2].to_i).to_date
  end

  def self.parse_float(value)
    return value if value.is_a?(Numeric)
    return nil if value.blank? || !value.is_a?(String)

    value.to_f
  end

  def self.parse_payment_schedule(loan_setup_data)
    return [] if loan_setup_data.blank? || loan_setup_data['tilPaymentSchedule'].blank?

    JSON.parse(loan_setup_data['tilPaymentSchedule'])
  end
end
