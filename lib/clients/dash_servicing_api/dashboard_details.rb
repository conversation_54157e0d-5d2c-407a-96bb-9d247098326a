# frozen_string_literal: true

module Clients
  class DashServicingApi
    class DashboardDetails < Base
      # Attributes from https://github.com/Above-Lending/dash/blob/main/app/services/loan_management/fetch_loan_and_borrower_details.rb
      attribute :apr, :string
      attribute :loan_amount, :float
      attribute :underwriting, :float
      attribute :loan_payment, :decimal
      attribute :number_of_terms, :integer
      attribute :contract_date, :date
      attribute :current_due_date, :date
      attribute :current_payment_due, :decimal
      attribute :current_payment_date, :date
      attribute :days_past_due, :integer
      attribute :loan_status_text, :string
      attribute :number_of_remaining_terms, :integer
      attribute :next_payment_amount, :decimal
      attribute :next_payment_date, :date
      attribute :overdue_amount, :decimal
      attribute :payment_frequency, :string
      attribute :remaining_balance, :decimal
      attribute :sub_status, :string
      attribute :sub_status_id, :integer
      attribute :last_payment
      attribute :borrower_name, :string
      attribute :address, :string
      attribute :city, :string
      attribute :state, :string
      attribute :zip_code, :string
      attribute :debt_sale, :boolean
      attribute :beneficial_owner_name, :string
      attribute :beneficial_owner_details

      def initialize(hash)
        details = hash.dup

        last_payment = details['last_payment']
        details['last_payment'] = last_payment.blank? ? nil : LastPayment.new(last_payment)

        if details['beneficial_owner_details']
          details['beneficial_owner_details'] = BeneficialOwner.new(details['beneficial_owner_details'])
        end

        super(details)
      end

      def debt_sale?
        debt_sale
      end

      def paid_off?
        loan_status_text == 'Paid Off'
      end

      def charged_off?
        sub_status == LoanproLoan::CLOSED_CHARGED_OFF_SUB_STATUS
      end

      def past_due?
        sub_status == 'Past Due'
      end

      def initial_amount
        loan_amount.to_f + underwriting.to_f
      end

      def amount_due
        return remaining_balance if charged_off?

        next_payment_amount
      end
    end
  end
end
