# frozen_string_literal: true

# Download eligilibility file from s3 bucket
module Clients
  module BeyondEligibilityFiles
    class << self
      def retrieve(target_date)
        return Rails.root.join('tmp', 'ipl_identification_mock_20250429-10.csv').to_s

        @date = target_date
        temp = Tempfile.new
        begin
          # Download the file from the S3 bucket
          temp.binmode
          s3_client.get_object(s3_inputs.merge(response_target: temp.path))
          temp.path
        rescue StandardError => e
          ExceptionLogger.error(e)
          Rails.logger.error("An error occurred: #{e.message}", key: "active/#{file_path}", target_date: @date.to_s)
          nil # Return nil to indicate an error
        end
      end

      def archive(target_date)
        @date = target_date
        # Move the file to the archive directory in S3
        archive_key = "archive/#{file_path}"

        begin
          s3_client.copy_object(
            bucket: s3_bucket,
            copy_source: "#{s3_bucket}/#{s3_inputs[:key]}",
            key: archive_key
          )

          # Delete the original file
          s3_client.delete_object(s3_inputs)
        rescue StandardError => e
          ExceptionLogger.error(e)
          nil # Return nil to indicate an error
        end
      end

      private

      def s3_client
        @s3_client ||= Aws::S3::Client.new
      end

      def s3_bucket
        @s3_bucket ||= ENV.fetch('AMS_DATA_S3_BUCKET', nil) ||
                       raise('AMS_DATA_S3_BUCKET is not set')
      end

      def s3_inputs
        {
          bucket: s3_bucket,
          key: "active/#{file_path}"
        }
      end

      def file_path
        today = @date&.strftime('%Y%m%d')
        env = Rails.env.production? ? '' : 'mock_'
        "ipl_identification_#{env}#{today}.csv"
      end

      include ::NewRelic::Agent::MethodTracer
      add_method_tracer :retrieve
    end
  end
end
